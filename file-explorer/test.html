<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Electron Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        h1 {
            color: #333;
        }
        button {
            background-color: #4CAF50;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
            margin-top: 10px;
        }
        button:hover {
            background-color: #45a049;
        }
        pre {
            background-color: #f0f0f0;
            padding: 10px;
            border-radius: 4px;
            overflow: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Electron Test Page</h1>
        <p>If you can see this page, Electron is working correctly!</p>
        
        <h2>Environment Information</h2>
        <pre id="env-info">Loading...</pre>
        
        <h2>Test Electron API</h2>
        <button id="test-api">Test Electron API</button>
        <pre id="api-result">Click the button to test the Electron API</pre>
    </div>

    <script>
        // Display environment information
        document.getElementById('env-info').textContent = `
Node.js version: ${process.versions.node}
Chrome version: ${process.versions.chrome}
Electron version: ${process.versions.electron}
`;

        // Test Electron API
        document.getElementById('test-api').addEventListener('click', async () => {
            const resultElement = document.getElementById('api-result');
            
            try {
                if (window.electronAPI) {
                    resultElement.textContent = 'Electron API is available!';
                } else {
                    resultElement.textContent = 'Error: Electron API is not available';
                }
            } catch (error) {
                resultElement.textContent = `Error: ${error.message}`;
            }
        });
    </script>
</body>
</html>
