const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

// Get the path to the electron executable
const electronPath = path.join(__dirname, 'node_modules', '.bin', 'electron');
const electronPathWindows = path.join(__dirname, 'node_modules', '.bin', 'electron.cmd');

// Check if the electron executable exists
const electronExists = fs.existsSync(electronPath) || fs.existsSync(electronPathWindows);
if (!electronExists) {
  console.error('Electron executable not found. Make sure electron is installed.');
  process.exit(1);
}

// Check if the out directory exists
const outDir = path.join(__dirname, 'out');
if (!fs.existsSync(outDir)) {
  console.error('Out directory not found. Make sure you have built the app.');
  process.exit(1);
}

// Check if the index.html file exists
const indexPath = path.join(outDir, 'index.html');
if (!fs.existsSync(indexPath)) {
  console.error('index.html not found. Make sure you have built the app.');
  process.exit(1);
}

// Run electron with the current directory
const electronExe = process.platform === 'win32' ? electronPathWindows : electronPath;
const electron = spawn(electronExe, ['.'], {
  stdio: 'inherit',
  env: {
    ...process.env,
    ELECTRON_ENABLE_LOGGING: 1,
    ELECTRON_ENABLE_STACK_DUMPING: 1,
    DEBUG: '*'
  }
});

electron.on('close', (code) => {
  console.log(`Electron process exited with code ${code}`);
});
