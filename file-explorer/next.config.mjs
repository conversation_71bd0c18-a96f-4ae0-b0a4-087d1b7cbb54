/** @type {import('next').NextConfig} */
const nextConfig = {
  eslint: {
    ignoreDuringBuilds: true,
  },
  typescript: {
    ignoreBuildErrors: true,
  },
  images: {
    unoptimized: true,
  },
  // Configure webpack for Monaco Editor
  webpack: (config, { isServer }) => {
    // Only modify client-side webpack config
    if (!isServer) {
      // Make sure workers are properly handled
      config.output.globalObject = 'self';

      // Ensure Monaco Editor's workers are properly handled
      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false,
        path: false,
        os: false,
      };
    }

    // Important: Return the modified config
    return config;
  },
}

export default nextConfig