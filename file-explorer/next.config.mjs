/** @type {import('next').NextConfig} */
const nextConfig = {
  output: 'export', // Added for static export
  eslint: {
    ignoreDuringBuilds: true,
  },
  typescript: {
    ignoreBuildErrors: true,
  },
  images: {
    unoptimized: true,
  },
  // Ensure Next.js knows this is a static export
  trailingSlash: true,
  // Disable server components for static export
  serverExternalPackages: [],
  // Ensure assets are correctly referenced
  assetPrefix: '/',
  // Disable source maps in production
  productionBrowserSourceMaps: false,
  // Disable React strict mode for compatibility
  reactStrictMode: false,
  // No custom webpack configuration for Monaco Editor
  // Let Next.js handle it with its default configuration
}

export default nextConfig