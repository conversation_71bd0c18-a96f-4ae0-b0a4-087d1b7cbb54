/**
 * Board Agent API
 *
 * This service provides methods for AI agents to interact with the Kanban board.
 */

export class BoardAgentAPI {
  private boardContext: any

  constructor(boardContext: any) {
    this.boardContext = boardContext
  }

  /**
   * Get the current state of the board
   */
  getBoardState() {
    return {
      columns: this.boardContext.columns,
      cards: this.getAllCards(),
      swimlanes: this.boardContext.swimlanes,
    }
  }

  /**
   * Create a new task card
   */
  createTaskCard(task: any, agentId: string) {
    const now = new Date().toISOString()

    const newCard = {
      id: `card-${Date.now()}`,
      title: task.title,
      description: task.description,
      priority: task.priority || "medium",
      projectId: task.projectId || `TASK-${Math.floor(Math.random() * 1000)}`,
      dueDate: task.dueDate,
      assignee: task.assignee,
      tags: task.tags || [],
      subtasks: task.subtasks || [],
      swimlaneId: task.swimlaneId || "default",
      storyPoints: task.storyPoints,
      agentAssignments: [
        {
          agentId,
          agentType: "AI",
          assignmentTime: now,
        },
      ],
      dependencies: task.dependencies || [],
      resourceMetrics: {
        tokenUsage: 0,
        cpuTime: 0,
        memoryUsage: 0,
      },
      taskHistory: [
        {
          timestamp: now,
          action: "created",
          agentId,
          details: "Card created by AI agent",
        },
      ],
      // Ensure columnId is present for addCard to work correctly in board-context
      columnId: task.columnId || (this.boardContext.columns && this.boardContext.columns.length > 0 ? this.boardContext.columns[0].id : "column-1"), // Default to first column if available
      progress: task.progress || 0, // Ensure progress is initialized
      labels: task.labels || [], // Ensure labels are initialized
      createdAt: now,
      updatedAt: now,
    }

    // Add the card to the first column (usually backlog)
    // The boardContext.addCard method expects boardId, columnId, and cardData (without id).
    // The adapter in agent-board-controller.tsx maps this.boardContext.addCard(newCard)
    // to boardContext.addCardToColumn(activeBoard.id, newCard.columnId, newCard).
    // So `newCard` must contain `columnId`.
    this.boardContext.addCard(newCard)

    return newCard
  }

  /**
   * Move a card to a different column
   */
  moveCardToColumn(cardId: string, columnId: string, agentId: string) {
    const card = this.getCardById(cardId)
    if (!card) return null

    const sourceColumnId = card.columnId
    const swimlaneId = card.swimlaneId || "default" // Keep existing swimlane or default

    // Add history entry
    const updatedCard = {
      ...card,
      columnId,
      // Pass the swimlaneId when moving, as moveCard in board-context needs it
      swimlaneId,
      taskHistory: [
        ...card.taskHistory,
        {
          timestamp: new Date().toISOString(),
          action: "moved",
          agentId,
          details: `Moved from column ${sourceColumnId} to ${columnId}`,
        },
      ],
    }

    // Call moveCard directly if the context provides it, or updateCard if not.
    // The adapter in agent-board-controller.tsx maps boardContext.updateCard(updatedCard)
    // to boardContext.updateCardInColumn(activeBoard.id, updatedCard.columnId, updatedCard).
    // For a real move, it's better to call the moveCard method in boardContext directly.
    // However, the current adapter only maps updateCard. So, this will trigger an update in the UI.
    // Future improvement: expose `moveCard` directly in the adapter.
    // For now, rely on `updateCard` which should trigger the UI update correctly.
    // The `moveCard` function in `board-context` already handles removal from source and adding to destination.
    // Let's call the `moveCard` function of the context if it exists, otherwise fallback to `updateCard`.
    if (typeof this.boardContext.moveCard === 'function') {
        // This is a direct call for a real move, the `moveCard` in board-context needs specific args
        // This will require boardContext adapter to expose `moveCard` similarly to `addCard`.
        // Given current adapter, it relies on updateCard which is less efficient for moves but works.
        // For now, `updateCard` is sufficient for internal state change.
        this.boardContext.updateCard(updatedCard) // This will update the card object in its existing column
    } else {
        this.boardContext.updateCard(updatedCard)
    }

    return updatedCard
  }

  /**
   * Update card progress
   */
  updateCardProgress(cardId: string, progress: number, agentId: string) {
    const card = this.getCardById(cardId)
    if (!card) return null

    const updatedCard = {
      ...card,
      progress: Math.min(100, Math.max(0, progress)),
      taskHistory: [
        ...card.taskHistory,
        {
          timestamp: new Date().toISOString(),
          action: "progress-update",
          agentId,
          details: `Updated progress to ${progress}%`,
        },
      ],
    }

    this.boardContext.updateCard(updatedCard)

    return updatedCard
  }

  /**
   * Update card resource metrics
   */
  updateCardResourceMetrics(cardId: string, metrics: any, agentId: string) {
    const card = this.getCardById(cardId)
    if (!card) return null

    const updatedCard = {
      ...card,
      resourceMetrics: {
        ...card.resourceMetrics,
        ...metrics,
      },
      taskHistory: [
        ...card.taskHistory,
        {
          timestamp: new Date().toISOString(),
          action: "resource-update",
          agentId,
          details: `Updated resource metrics`,
        },
      ],
    }

    this.boardContext.updateCard(updatedCard)

    return updatedCard
  }

  /**
   * Add a dependency between cards
   */
  addCardDependency(cardId: string, dependencyCardId: string, agentId: string) {
    const card = this.getCardById(cardId)
    if (!card) return null

    // Don't add if already a dependency
    if (card.dependencies.includes(dependencyCardId)) {
      return card
    }

    const updatedCard = {
      ...card,
      dependencies: [...card.dependencies, dependencyCardId],
      taskHistory: [
        ...card.taskHistory,
        {
          timestamp: new Date().toISOString(),
          action: "dependency-added",
          agentId,
          details: `Added dependency on card ${dependencyCardId}`,
        },
      ],
    }

    this.boardContext.updateCard(updatedCard)

    return updatedCard
  }

  /**
   * Remove a dependency between cards
   */
  removeCardDependency(cardId: string, dependencyCardId: string, agentId: string) {
    const card = this.getCardById(cardId)
    if (!card) return null

    const updatedCard = {
      ...card,
      dependencies: card.dependencies.filter((id) => id !== dependencyCardId),
      taskHistory: [
        ...card.taskHistory,
        {
          timestamp: new Date().toISOString(),
          action: "dependency-removed",
          agentId,
          details: `Removed dependency on card ${dependencyCardId}`,
        },
      ],
    }

    this.boardContext.updateCard(updatedCard)

    return updatedCard
  }

  /**
   * Get all cards across all columns
   */
  private getAllCards() {
    if (!this.boardContext.columns) return []
    return this.boardContext.columns.flatMap((column: any) => column.cards)
  }

  /**
   * Get a card by ID
   */
  private getCardById(cardId: string) {
    const allCards = this.getAllCards()
    return allCards.find((card: any) => card.id === cardId)
  }
}