"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const electron_1 = require("electron");
const path_1 = __importDefault(require("path"));
const url_1 = __importDefault(require("url"));
const fs_1 = __importDefault(require("fs"));
const electron_is_dev_1 = __importDefault(require("electron-is-dev"));
let mainWindow;
function createWindow() {
    mainWindow = new electron_1.BrowserWindow({
        width: 1200,
        height: 800,
        webPreferences: {
            nodeIntegration: false, // Best practice for security
            contextIsolation: true, // Best practice for security
            preload: path_1.default.join(__dirname, 'preload.js'), // Enable preload script for IPC
            devTools: true, // Always enable DevTools for debugging
            webSecurity: true, // Enable web security
        },
        icon: path_1.default.join(__dirname, '../public/placeholder-logo.svg') // Adjust path if needed
    });
    // Set Content Security Policy
    mainWindow.webContents.session.webRequest.onHeadersReceived((details, callback) => {
        callback({
            responseHeaders: {
                ...details.responseHeaders,
                'Content-Security-Policy': [
                    "default-src 'self'; script-src 'self' 'unsafe-eval' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data:; font-src 'self' data:; connect-src 'self' ws: wss:;"
                ]
            }
        });
    });
    // Set a default title
    mainWindow.setTitle('CodeFusion - Modern Code Editor');
    // Determine the correct path to load
    const appPath = electron_1.app.getAppPath();
    console.log('App path:', appPath);
    // Try to load the test HTML file first
    const testHtmlPath = path_1.default.join(process.cwd(), 'test.html');
    const testHtmlPathAlt = path_1.default.join(__dirname, '../test.html');
    console.log('Looking for test.html at:', testHtmlPath);
    console.log('Alternative path:', testHtmlPathAlt);
    if (fs_1.default.existsSync(testHtmlPath)) {
        console.log('Found test.html at:', testHtmlPath);
        mainWindow.loadFile(testHtmlPath);
        mainWindow.webContents.openDevTools();
    }
    else if (fs_1.default.existsSync(testHtmlPathAlt)) {
        console.log('Found test.html at:', testHtmlPathAlt);
        mainWindow.loadFile(testHtmlPathAlt);
        mainWindow.webContents.openDevTools();
    }
    else if (electron_is_dev_1.default || process.argv.includes('--dev')) {
        // In development, load from the Next.js dev server
        console.log('Loading from dev server');
        mainWindow.loadURL('http://localhost:3000');
        mainWindow.webContents.openDevTools();
    }
    else {
        // In production, load the statically exported Next.js app
        try {
            // Find the correct path to the out directory
            const outDir = path_1.default.join(__dirname, '../out');
            const outDirAlt = path_1.default.join(process.cwd(), 'out');
            console.log('Looking for out directory at:', outDir);
            console.log('Alternative path:', outDirAlt);
            let indexPath;
            if (fs_1.default.existsSync(path_1.default.join(outDir, 'index.html'))) {
                indexPath = url_1.default.format({
                    pathname: path_1.default.join(outDir, 'index.html'),
                    protocol: 'file:',
                    slashes: true,
                });
            }
            else if (fs_1.default.existsSync(path_1.default.join(outDirAlt, 'index.html'))) {
                indexPath = url_1.default.format({
                    pathname: path_1.default.join(outDirAlt, 'index.html'),
                    protocol: 'file:',
                    slashes: true,
                });
            }
            else {
                throw new Error('Could not find index.html in out directory');
            }
            console.log('Loading from:', indexPath);
            mainWindow.loadURL(indexPath);
            mainWindow.webContents.openDevTools(); // Open DevTools in production for debugging
        }
        catch (error) {
            console.error('Error loading index.html:', error);
            // Show error in window
            mainWindow.loadURL(`data:text/html,<html><body><h1>Error</h1><p>${error}</p></body></html>`);
        }
    }
    // Open external links in the default browser
    mainWindow.webContents.setWindowOpenHandler(({ url }) => {
        electron_1.shell.openExternal(url);
        return { action: 'deny' };
    });
    mainWindow.on('closed', () => {
        mainWindow = null;
    });
}
electron_1.app.on('ready', createWindow);
electron_1.app.on('window-all-closed', () => {
    if (process.platform !== 'darwin') {
        electron_1.app.quit();
    }
});
electron_1.app.on('activate', () => {
    if (mainWindow === null) {
        createWindow();
    }
});
// IPC handlers for file system operations
electron_1.ipcMain.handle('select-folder', async () => {
    const result = await electron_1.dialog.showOpenDialog(mainWindow, {
        properties: ['openDirectory'],
        title: 'Select Project Folder'
    });
    if (!result.canceled && result.filePaths.length > 0) {
        const folderPath = result.filePaths[0];
        return {
            success: true,
            path: folderPath,
            name: path_1.default.basename(folderPath)
        };
    }
    return { success: false };
});
electron_1.ipcMain.handle('read-directory', async (event, dirPath) => {
    try {
        const items = fs_1.default.readdirSync(dirPath, { withFileTypes: true });
        const result = [];
        for (const item of items) {
            const itemPath = path_1.default.join(dirPath, item.name);
            const stats = fs_1.default.statSync(itemPath);
            if (item.isDirectory()) {
                result.push({
                    id: Date.now() + Math.random(),
                    name: item.name,
                    type: 'folder',
                    path: itemPath,
                    expanded: false,
                    files: []
                });
            }
            else {
                const ext = path_1.default.extname(item.name).slice(1);
                result.push({
                    id: Date.now() + Math.random(),
                    name: item.name,
                    type: ext || 'file',
                    path: itemPath,
                    size: stats.size,
                    modified: stats.mtime
                });
            }
        }
        return { success: true, items: result };
    }
    catch (error) {
        console.error('Error reading directory:', error);
        return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
});
electron_1.ipcMain.handle('read-file', async (event, filePath) => {
    try {
        const content = fs_1.default.readFileSync(filePath, 'utf-8');
        return { success: true, content };
    }
    catch (error) {
        console.error('Error reading file:', error);
        return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
});
electron_1.ipcMain.handle('save-file', async (event, filePath, content) => {
    try {
        fs_1.default.writeFileSync(filePath, content, 'utf-8');
        return { success: true };
    }
    catch (error) {
        console.error('Error saving file:', error);
        return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
});
electron_1.ipcMain.handle('create-file', async (event, filePath, content = '') => {
    try {
        // Check if file already exists
        if (fs_1.default.existsSync(filePath)) {
            return { success: false, error: 'File already exists' };
        }
        // Create directory if it doesn't exist
        const dir = path_1.default.dirname(filePath);
        if (!fs_1.default.existsSync(dir)) {
            fs_1.default.mkdirSync(dir, { recursive: true });
        }
        fs_1.default.writeFileSync(filePath, content, 'utf-8');
        return { success: true };
    }
    catch (error) {
        console.error('Error creating file:', error);
        return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
});
electron_1.ipcMain.handle('delete-file', async (event, filePath) => {
    try {
        const stats = fs_1.default.statSync(filePath);
        if (stats.isDirectory()) {
            fs_1.default.rmSync(filePath, { recursive: true, force: true });
        }
        else {
            fs_1.default.unlinkSync(filePath);
        }
        return { success: true };
    }
    catch (error) {
        console.error('Error deleting file:', error);
        return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
});
