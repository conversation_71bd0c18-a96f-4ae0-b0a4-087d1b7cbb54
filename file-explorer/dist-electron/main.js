"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const electron_1 = require("electron");
const path_1 = __importDefault(require("path"));
const url_1 = __importDefault(require("url"));
const fs_1 = __importDefault(require("fs"));
const electron_is_dev_1 = __importDefault(require("electron-is-dev"));
// Create a safer console.log that doesn't throw when streams are closed
const safeConsole = {
    log: (...args) => {
        try {
            console.log(...args);
        }
        catch (error) {
            // Silently ignore write errors
        }
    },
    error: (...args) => {
        try {
            console.error(...args);
        }
        catch (error) {
            // Silently ignore write errors
        }
    }
};
let mainWindow;
function createWindow() {
    mainWindow = new electron_1.BrowserWindow({
        width: 1200,
        height: 800,
        webPreferences: {
            nodeIntegration: false, // Best practice for security
            contextIsolation: true, // Best practice for security
            preload: path_1.default.join(__dirname, 'preload.js'), // Enable preload script for IPC
            devTools: true, // Always enable DevTools for debugging
            webSecurity: true, // Enable web security
        },
        icon: path_1.default.join(__dirname, '../public/placeholder-logo.svg') // Adjust path if needed
    });
    // Set Content Security Policy - More secure configuration
    mainWindow.webContents.session.webRequest.onHeadersReceived((details, callback) => {
        callback({
            responseHeaders: {
                ...details.responseHeaders,
                'Content-Security-Policy': [
                    "default-src 'self'; " +
                        "script-src 'self' 'unsafe-eval' 'unsafe-inline' blob: data:; " +
                        "style-src 'self' 'unsafe-inline' data:; " +
                        "img-src 'self' data: blob: https:; " +
                        "font-src 'self' data: blob:; " +
                        "connect-src 'self' ws: wss: http: https:; " +
                        "worker-src 'self' blob: data:; " +
                        "child-src 'self' blob: data:; " +
                        "frame-src 'self' blob: data:; " +
                        "media-src 'self' blob: data:;"
                ]
            }
        });
    });
    // Set a default title
    mainWindow.setTitle('CodeFusion - Modern Code Editor');
    // Determine the correct path to load
    const appPath = electron_1.app.getAppPath();
    safeConsole.log('App path:', appPath);
    if (electron_is_dev_1.default || process.argv.includes('--dev')) {
        // In development, load from the Next.js dev server
        safeConsole.log('Loading from dev server');
        mainWindow.loadURL('http://localhost:4444');
        mainWindow.webContents.openDevTools();
    }
    else {
        // In production, load the statically exported Next.js app
        try {
            // Use a direct path to the index.html file
            const indexFile = path_1.default.join(__dirname, '../out/index.html');
            safeConsole.log('Checking for index.html at:', indexFile);
            if (fs_1.default.existsSync(indexFile)) {
                safeConsole.log('Found index.html at:', indexFile);
                const indexPath = url_1.default.format({
                    pathname: indexFile,
                    protocol: 'file:',
                    slashes: true,
                });
                safeConsole.log('Loading from:', indexPath);
                mainWindow.loadFile(indexFile);
            }
            else {
                // Try alternative paths
                const possiblePaths = [
                    path_1.default.join(process.cwd(), 'out/index.html'),
                    path_1.default.join(electron_1.app.getAppPath(), 'out/index.html')
                ];
                safeConsole.log('Checking alternative paths:');
                possiblePaths.forEach(p => safeConsole.log(' - ' + p));
                let found = false;
                for (const altPath of possiblePaths) {
                    if (fs_1.default.existsSync(altPath)) {
                        safeConsole.log('Found index.html at:', altPath);
                        mainWindow.loadFile(altPath);
                        found = true;
                        break;
                    }
                }
                if (!found) {
                    throw new Error('Could not find index.html in any of the expected locations');
                }
            }
            // Only open DevTools in development or when explicitly requested
            if (process.argv.includes('--devtools')) {
                mainWindow.webContents.openDevTools();
            }
        }
        catch (error) {
            safeConsole.error('Error loading index.html:', error);
            // Show error in window
            mainWindow.loadURL(`data:text/html,<html><body><h1>Error</h1><p>${error}</p></body></html>`);
            // Always open DevTools when there's an error
            mainWindow.webContents.openDevTools();
        }
    }
    // Open external links in the default browser
    mainWindow.webContents.setWindowOpenHandler(({ url }) => {
        electron_1.shell.openExternal(url);
        return { action: 'deny' };
    });
    mainWindow.on('closed', () => {
        mainWindow = null;
    });
}
electron_1.app.on('ready', createWindow);
electron_1.app.on('window-all-closed', () => {
    if (process.platform !== 'darwin') {
        electron_1.app.quit();
    }
});
electron_1.app.on('activate', () => {
    if (mainWindow === null) {
        createWindow();
    }
});
// IPC handlers for file system operations
electron_1.ipcMain.handle('select-folder', async () => {
    const result = await electron_1.dialog.showOpenDialog(mainWindow, {
        properties: ['openDirectory'],
        title: 'Select Project Folder'
    });
    if (!result.canceled && result.filePaths.length > 0) {
        const folderPath = result.filePaths[0];
        return {
            success: true,
            path: folderPath,
            name: path_1.default.basename(folderPath)
        };
    }
    return { success: false };
});
electron_1.ipcMain.handle('read-directory', async (event, dirPath) => {
    try {
        const items = fs_1.default.readdirSync(dirPath, { withFileTypes: true });
        const result = [];
        for (const item of items) {
            const itemPath = path_1.default.join(dirPath, item.name);
            const stats = fs_1.default.statSync(itemPath);
            if (item.isDirectory()) {
                result.push({
                    id: Date.now() + Math.random(),
                    name: item.name,
                    type: 'folder',
                    path: itemPath,
                    expanded: false,
                    files: []
                });
            }
            else {
                const ext = path_1.default.extname(item.name).slice(1);
                result.push({
                    id: Date.now() + Math.random(),
                    name: item.name,
                    type: ext || 'file',
                    path: itemPath,
                    size: stats.size,
                    modified: stats.mtime
                });
            }
        }
        return { success: true, items: result };
    }
    catch (error) {
        safeConsole.error('Error reading directory:', error);
        return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
});
electron_1.ipcMain.handle('read-file', async (event, filePath) => {
    try {
        const content = fs_1.default.readFileSync(filePath, 'utf-8');
        return { success: true, content };
    }
    catch (error) {
        safeConsole.error('Error reading file:', error);
        return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
});
electron_1.ipcMain.handle('save-file', async (event, filePath, content) => {
    try {
        fs_1.default.writeFileSync(filePath, content, 'utf-8');
        return { success: true };
    }
    catch (error) {
        safeConsole.error('Error saving file:', error);
        return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
});
electron_1.ipcMain.handle('create-file', async (event, filePath, content = '') => {
    try {
        // Check if file already exists
        if (fs_1.default.existsSync(filePath)) {
            return { success: false, error: 'File already exists' };
        }
        // Create directory if it doesn't exist
        const dir = path_1.default.dirname(filePath);
        if (!fs_1.default.existsSync(dir)) {
            fs_1.default.mkdirSync(dir, { recursive: true });
        }
        fs_1.default.writeFileSync(filePath, content, 'utf-8');
        return { success: true };
    }
    catch (error) {
        safeConsole.error('Error creating file:', error);
        return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
});
electron_1.ipcMain.handle('delete-file', async (event, filePath) => {
    try {
        const stats = fs_1.default.statSync(filePath);
        if (stats.isDirectory()) {
            fs_1.default.rmSync(filePath, { recursive: true, force: true });
        }
        else {
            fs_1.default.unlinkSync(filePath);
        }
        return { success: true };
    }
    catch (error) {
        safeConsole.error('Error deleting file:', error);
        return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
});
