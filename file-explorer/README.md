# CodeFusion - Modern Code Editor

A modern code editor built with Next.js and Electron.

## Getting Started

### Prerequisites

- Node.js (v18 or later)
- npm or yarn

### Installation

1. Clone the repository
2. Install dependencies:

```bash
cd file-explorer
npm install
```

### Development

To run the application in development mode:

```bash
npm run electron:dev
```

This will start the Next.js development server and launch the Electron app.

### Production Build

To build the application for production:

```bash
npm run rebuild
```

This will:
1. Clean the output directories
2. Build the Next.js application
3. Compile the Electron TypeScript files

To run the production build:

```bash
npm run start
```

## Troubleshooting

If you encounter issues with the application not loading:

1. Try running the simple Electron app to verify that Electron is working:

```bash
npm run simple
```

2. Check if the test.html file is being loaded:

```bash
node run-electron-verbose.js
```

3. Make sure the Next.js application is built correctly:

```bash
npm run build
```

4. Check if the Electron main process is finding the correct paths:

```bash
npm run compile:electron
```

## Project Structure

- `app/`: Next.js application files
- `components/`: React components
- `electron/`: Electron main and preload scripts
- `dist-electron/`: Compiled Electron scripts
- `out/`: Next.js static export
- `public/`: Static assets
- `styles/`: CSS styles
- `types/`: TypeScript type definitions

## Features

- File explorer
- Code editor with syntax highlighting
- Terminal integration
- Kanban board
- Agent system
- Dark/light theme support
