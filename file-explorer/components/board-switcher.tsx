"use client"

import * as React from "react"
import { Check, ChevronsUpDown, PlusCircle } from "lucide-react"
import { useRouter } from "next/navigation"

import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
  CommandSeparator,
} from "@/components/ui/command"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import { useBoard } from "@/components/kanban/board-context"
import { useToast } from "@/components/ui/use-toast"

interface BoardSwitcherProps {
  onBoardChange?: (boardId: string) => void;
}

export function BoardSwitcher({ onBoardChange }: BoardSwitcherProps) {
  const [open, setOpen] = React.useState(false)
  const { boards, activeBoard, setActiveBoard, addBoard } = useBoard()
  const { toast } = useToast();
  const router = useRouter();

  const handleSelectBoard = (boardId: string) => {
    setActiveBoard(boardId);
    setOpen(false);
    if (onBoardChange) {
      onBoardChange(boardId);
    } else {
      router.push(`/?board=${boardId}`);
    }
    toast({
      title: "Board Switched",
      description: `You are now on board: ${boards.find(b => b.id === boardId)?.name}`,
    });
  };

  const handleCreateNewBoard = () => {
    const newBoard = addBoard(`New Board ${boards.length + 1}`);
    handleSelectBoard(newBoard.id);
  };

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          aria-label="Select a board"
          className="w-[200px] justify-between"
        >
          {activeBoard ? activeBoard.name : "Select board..."}
          <ChevronsUpDown className="ml-auto h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-[200px] p-0">
        <Command>
          <CommandList>
            <CommandInput placeholder="Search boards..." />
            <CommandEmpty>No board found.</CommandEmpty>
            <CommandGroup>
              {boards.map((board) => (
                <CommandItem
                  key={board.id}
                  onSelect={() => handleSelectBoard(board.id)}
                  className="text-sm"
                >
                  <Check
                    className={cn(
                      "mr-2 h-4 w-4",
                      activeBoard?.id === board.id ? "opacity-100" : "opacity-0"
                    )}
                  />
                  {board.name}
                </CommandItem>
              ))}
            </CommandGroup>
          </CommandList>
          <CommandSeparator />
          <CommandList>
            <CommandGroup>
              <CommandItem onSelect={handleCreateNewBoard}>
                <PlusCircle className="mr-2 h-4 w-4" />
                Create New Board
              </CommandItem>
            </CommandGroup>
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  )
}