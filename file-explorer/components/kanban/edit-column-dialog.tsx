"use client"

import React, { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Column } from "./board-context" // Assuming Column type from board-context

interface EditColumnDialogProps {
  isOpen: boolean
  onClose: () => void
  column: Column
  onSave: (updatedColumn: Column) => void
}

export function EditColumnDialog({ isOpen, onClose, column, onSave }: EditColumnDialogProps) {
  const [title, setTitle] = useState(column.title)
  const [color, setColor] = useState(column.metadata?.color || "#e2e8f0") // Default color

  useEffect(() => {
    setTitle(column.title)
    setColor(column.metadata?.color || "#e2e8f0")
  }, [column])

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    onSave({
      ...column,
      title: title,
      metadata: {
        ...column.metadata,
        color: color,
      },
    })
    onClose()
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Edit Column</DialogTitle>
        </DialogHeader>
        <form onSubmit={handleSubmit}>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="title" className="text-right text-sm">
                Title
              </Label>
              <Input
                id="title"
                value={title}
                onChange={(e) => setTitle(e.target.value)}
                className="col-span-3"
                required
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="color" className="text-right text-sm">
                Color
              </Label>
              <Input
                id="color"
                type="color"
                value={color}
                onChange={(e) => setColor(e.target.value)}
                className="col-span-3 h-10 w-full"
              />
            </div>
          </div>
          <DialogFooter>
            <Button type="button" variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button type="submit">Save Changes</Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}