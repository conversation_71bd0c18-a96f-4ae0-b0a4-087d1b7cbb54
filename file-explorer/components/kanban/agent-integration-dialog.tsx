"use client"

import React, { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Checkbox } from "@/components/ui/checkbox"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { Separator } from "@/components/ui/separator"
import { AlertTriangle, Cpu, Play, Pause, X } from "lucide-react"
import { useAgentBoardController } from "./agent-board-controller" // Assuming agent-board-controller is in the same folder
import { ScrollArea } from "@/components/ui/scroll-area"

interface AgentIntegrationDialogProps {}

export function AgentIntegrationDialog({}: AgentIntegrationDialogProps) {
  const [open, setOpen] = useState(false)
  const {
    isAgentRunning,
    startAgent,
    stopAgent,
    agentLogs,
    clearLogs,
    agentStatus,
  } = useAgentBoardController();

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" size="sm">
          Agent Control
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[700px] max-h-[90vh] flex flex-col">
        <DialogHeader>
          <DialogTitle>AI Agent Control Panel</DialogTitle>
        </DialogHeader>
        <div className="grid grid-cols-2 gap-4 flex-1 overflow-hidden py-4">
          {/* Left Pane: Agent Status & Control */}
          <div className="flex flex-col border rounded-md p-4 space-y-4">
            <h3 className="font-medium text-lg">System Status</h3>
            <div className="flex items-center gap-2">
              <div
                className={`w-3 h-3 rounded-full ${
                  agentStatus === "running"
                    ? "bg-green-500"
                    : agentStatus === "paused"
                    ? "bg-yellow-500"
                    : agentStatus === "error"
                    ? "bg-red-500"
                    : "bg-gray-500"
                }`}
              />
              <span className="text-sm font-medium">
                Overall Status:{" "}
                {agentStatus.charAt(0).toUpperCase() + agentStatus.slice(1)}
              </span>
            </div>

            <div className="flex gap-2">
              <Button onClick={startAgent} disabled={isAgentRunning} className="flex-1">
                <Play className="h-4 w-4 mr-2" /> Start Agents
              </Button>
              <Button onClick={stopAgent} disabled={!isAgentRunning} variant="outline" className="flex-1">
                <Pause className="h-4 w-4 mr-2" /> Stop Agents
              </Button>
            </div>

            <Separator />

            <h3 className="font-medium text-lg">Configuration</h3>
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <Label htmlFor="auto-delegate" className="text-sm">Auto-delegate tasks</Label>
                <Checkbox id="auto-delegate" checked={true} onCheckedChange={() => {}} />
              </div>
              <div className="flex items-center justify-between">
                <Label htmlFor="smart-context" className="text-sm">Smart context prefetching</Label>
                <Checkbox id="smart-context" checked={true} onCheckedChange={() => {}} />
              </div>
              <div className="grid gap-1">
                <Label htmlFor="default-llm" className="text-sm">Default LLM Endpoint</Label>
                <Input id="default-llm" value="https://api.openai.com/v1" readOnly />
              </div>
              <div className="grid gap-1">
                <Label htmlFor="micromanager-config" className="text-sm">Micromanager Config</Label>
                <Input id="micromanager-config" value="Default (Advanced)" readOnly />
              </div>
            </div>
          </div>

          {/* Right Pane: Agent Logs */}
          <div className="flex flex-col border rounded-md p-4 overflow-hidden">
            <h3 className="font-medium text-lg mb-2">Agent Logs</h3>
            <ScrollArea className="flex-1 border p-2 rounded-md bg-muted/10">
              <div className="space-y-2">
                {agentLogs.length > 0 ? (
                  agentLogs.map((log, index) => (
                    <p key={index} className="text-xs text-muted-foreground break-words">
                      {log}
                    </p>
                  ))
                ) : (
                  <div className="text-center text-sm text-muted-foreground py-8">
                    No logs yet. Start the agent system to see activity.
                  </div>
                )}
              </div>
            </ScrollArea>
            <div className="flex justify-end mt-4">
              <Button variant="outline" size="sm" onClick={clearLogs}>
                <X className="h-3.5 w-3.5 mr-2" /> Clear Logs
              </Button>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}