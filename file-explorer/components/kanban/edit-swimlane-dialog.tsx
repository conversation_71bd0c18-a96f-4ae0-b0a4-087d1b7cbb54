"use client"

import React, { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Swimlane } from "./board-context" // Assuming Swimlane type from board-context

interface EditSwimlaneDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  swimlane: Swimlane | null
  onUpdateSwimlane: (id: string, title: string) => void
}

export function EditSwimlaneDialog({
  open,
  onOpenChange,
  swimlane,
  onUpdateSwimlane,
}: EditSwimlaneDialogProps) {
  const [title, setTitle] = useState("")

  useEffect(() => {
    if (open && swimlane) {
      setTitle(swimlane.title)
    }
  }, [open, swimlane])

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    if (swimlane && title.trim()) {
      onUpdateSwimlane(swimlane.id, title)
      onOpenChange(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Edit Swimlane</DialogTitle>
        </DialogHeader>
        <form onSubmit={handleSubmit}>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="title" className="text-right text-sm">
                Title
              </Label>
              <Input
                id="title"
                value={title}
                onChange={(e) => setTitle(e.target.value)}
                className="col-span-3"
                required
              />
            </div>
          </div>
          <DialogFooter>
            <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
              Cancel
            </Button>
            <Button type="submit">Save Changes</Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}