"use client"

import { CardType } from "./board-context"
import { But<PERSON> } from "@/components/ui/button"
import { Settings } from "lucide-react"

interface KanbanLegendProps {
  cardTypes: CardType[]
  onEdit: () => void
}

export function KanbanLegend({ cardTypes, onEdit }: KanbanLegendProps) {
  return (
    <div className="flex items-center gap-2">
      <div className="flex items-center gap-1">
        {cardTypes.map((type) => (
          <div key={type.id} className="flex items-center gap-1">
            <div
              className="w-3 h-3 rounded-full"
              style={{ backgroundColor: type.color }}
            />
            <span className="text-xs">{type.name}</span>
          </div>
        ))}
      </div>
      <Button variant="ghost" size="icon" className="h-7 w-7" onClick={onEdit}>
        <Settings className="h-3.5 w-3.5" />
      </Button>
    </div>
  )
}