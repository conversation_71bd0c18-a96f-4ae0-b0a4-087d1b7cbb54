"use client"

import React, { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Di<PERSON>, Dialog<PERSON>ontent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Separator } from "@/components/ui/separator"
import { Edit } from "lucide-react"
import { CardType } from "./board-context" // Assuming CardType is available via board-context

interface KanbanLegendProps {
  cardTypes: CardType[];
  onEdit?: (e?: React.MouseEvent) => void;
}

export function KanbanLegend({ cardTypes, onEdit }: KanbanLegendProps) {
  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button variant="outline" size="sm">
          Legend
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Kanban Board Legend</DialogTitle>
        </DialogHeader>
        <div className="py-4">
          <h4 className="text-sm font-medium mb-2">Card Types</h4>
          <div className="space-y-2">
            {cardTypes.map((type) => (
              <div key={type.id} className="flex items-center gap-2">
                <div className="w-4 h-4 rounded-sm" style={{ backgroundColor: type.color }} />
                <span className="text-sm">{type.name}</span>
              </div>
            ))}
          </div>
          <Separator className="my-4" />
          <div className="flex justify-end">
            <Button variant="ghost" size="sm" onClick={onEdit}>
              <Edit className="mr-2 h-4 w-4" />
              Edit Legend
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}