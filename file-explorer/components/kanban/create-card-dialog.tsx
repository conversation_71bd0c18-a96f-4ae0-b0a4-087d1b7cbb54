"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { CardType } from "./board-context"
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON>Header,
  <PERSON>alog<PERSON>it<PERSON>,
  <PERSON>alogFooter,
} from "@/components/ui/dialog"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Label } from "@/components/ui/label"
import { Calendar } from "@/components/ui/calendar"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { CalendarIcon, Plus, Trash2 } from "lucide-react"
import { format } from "date-fns"
import { cn } from "@/lib/utils"
import { Checkbox } from "@/components/ui/checkbox"
import { Badge } from "@/components/ui/badge"

interface CreateCardDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onCreateCard: (card: any) => void
  cardTypes: CardType[]
}

export function CreateCardDialog({
  open,
  onOpenChange,
  onCreateCard,
  cardTypes,
}: CreateCardDialogProps) {
  const [title, setTitle] = useState("")
  const [description, setDescription] = useState("")
  const [priority, setPriority] = useState<string>("")
  const [projectId, setProjectId] = useState("")
  const [tags, setTags] = useState("")
  const [dueDate, setDueDate] = useState<Date | undefined>(undefined)
  const [assignee, setAssignee] = useState("")
  const [storyPoints, setStoryPoints] = useState<number | undefined>(undefined)
  const [subtasks, setSubtasks] = useState<{ id: string; title: string; completed: boolean }[]>([])
  const [newSubtask, setNewSubtask] = useState("")

  useEffect(() => {
    if (open && cardTypes.length > 0) {
      setPriority(cardTypes[0].id);
    } else if (open) {
      setPriority(""); // Fallback if no card types
    }
  }, [open, cardTypes]);

  const generateProjectId = () => {
    const cardType = cardTypes.find((type) => type.id === priority)
    const prefix = cardType
      ? cardType.name.substring(0, 4).toUpperCase()
      : "TASK"
    const randomNum = Math.floor(Math.random() * 900) + 100
    return `${prefix}-${randomNum}`
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    
    const newCard = {
      title,
      description,
      priority,
      projectId: projectId || generateProjectId(),
      tags: tags.split(",").map(tag => tag.trim()).filter(Boolean),
      progress: 0,
      labels: [],
      agentAssignments: [],
      dependencies: [],
      resourceMetrics: {
        tokenUsage: 0,
        cpuTime: 0,
        memoryUsage: 0,
      },
      taskHistory: [],
      dueDate: dueDate ? dueDate.toISOString() : undefined,
      assignee: assignee || undefined,
      storyPoints: storyPoints,
      subtasks: subtasks.length > 0 ? subtasks : undefined,
    }
    
    onCreateCard(newCard)
    resetForm()
    onOpenChange(false)
  }
  
  const resetForm = () => {
    setTitle("")
    setDescription("")
    if (cardTypes.length > 0) {
      setPriority(cardTypes[0].id);
    } else {
      setPriority("");
    }
    setProjectId("")
    setTags("")
    setDueDate(undefined)
    setAssignee("")
    setStoryPoints(undefined)
    setSubtasks([])
    setNewSubtask("")
  }

  const handleAddSubtask = () => {
    if (newSubtask.trim()) {
      setSubtasks([
        ...subtasks,
        {
          id: `subtask-${Date.now()}`,
          title: newSubtask.trim(),
          completed: false,
        },
      ])
      setNewSubtask("")
    }
  }

  const handleDeleteSubtask = (id: string) => {
    setSubtasks(subtasks.filter((subtask) => subtask.id !== id))
  }

  const handleToggleSubtask = (id: string) => {
    setSubtasks(
      subtasks.map((subtask) => (subtask.id === id ? { ...subtask, completed: !subtask.completed } : subtask)),
    )
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault()
      handleAddSubtask()
    }
  }

  const developmentTags = [
    "react",
    "node",
    "api",
    "database",
    "ui",
    "ux",
    "performance",
    "security",
    "testing",
    "mobile",
    "auth",
    "refactor",
    "documentation",
  ]

  const storyPointOptions = [1, 2, 3, 5, 8, 13, 21]

  return (
    <Dialog open={open} onOpenChange={(isOpen) => {
      if (!isOpen) resetForm();
      onOpenChange(isOpen);
    }}>
      <DialogContent className="sm:max-w-[500px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Create New Card</DialogTitle>
        </DialogHeader>
        <form onSubmit={handleSubmit}>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="title" className="text-right text-sm">
                Title
              </Label>
              <Input
                id="title"
                value={title}
                onChange={(e) => setTitle(e.target.value)}
                className="col-span-3"
                required
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="description" className="text-right text-sm">
                Description
              </Label>
              <Textarea
                id="description"
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                className="col-span-3"
                rows={3}
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="priority" className="text-right text-sm">
                Priority
              </Label>
              <Select
                value={priority}
                onValueChange={(value) => setPriority(value)}
                disabled={cardTypes.length === 0}
              >
                <SelectTrigger className="col-span-3">
                  <SelectValue placeholder="Select priority" />
                </SelectTrigger>
                <SelectContent>
                  {cardTypes.length > 0 ? (
                    cardTypes.map((type) => (
                      <SelectItem key={type.id} value={type.id}>
                        {type.name}
                      </SelectItem>
                    ))
                  ) : (
                    <SelectItem value="" disabled>No priorities configured</SelectItem>
                  )}
                </SelectContent>
              </Select>
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="projectId" className="text-right text-sm">
                Project ID
              </Label>
              <Input
                id="projectId"
                value={projectId}
                onChange={(e) => setProjectId(e.target.value)}
                className="col-span-3"
                placeholder="e.g. TASK-123"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="story-points" className="text-right text-sm">Story Points</Label>
              <select
                id="story-points"
                value={storyPoints?.toString() || ""}
                onChange={(e) => setStoryPoints(e.target.value ? Number.parseInt(e.target.value) : undefined)}
                className="col-span-3 flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
              >
                <option value="">Select points</option>
                {storyPointOptions.map((points) => (
                  <option key={points} value={points.toString()}>
                    {points}
                  </option>
                ))}
              </select>
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="dueDate" className="text-right text-sm">Due Date</Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn("col-span-3 w-full justify-start text-left font-normal", !dueDate && "text-muted-foreground")}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {dueDate ? format(dueDate, "PPP") : "Select a date"}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar mode="single" selected={dueDate} onSelect={setDueDate} initialFocus />
                </PopoverContent>
              </Popover>
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="assignee" className="text-right text-sm">Assignee</Label>
              <Input
                id="assignee"
                value={assignee}
                onChange={(e) => setAssignee(e.target.value)}
                className="col-span-3"
                placeholder="e.g., John Doe"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="tags" className="text-right text-sm">
                Tags
              </Label>
              <Input
                id="tags"
                value={tags}
                onChange={(e) => setTags(e.target.value)}
                className="col-span-3"
                placeholder="Comma-separated tags"
              />
            </div>
            <div className="col-span-4 col-start-2 flex flex-wrap gap-1">
                {developmentTags.map((tag) => (
                    <Badge
                        key={tag}
                        variant="outline"
                        className="cursor-pointer hover:bg-primary/10"
                        onClick={() => {
                            const currentTags = tags
                                .split(",")
                                .map((t) => t.trim())
                                .filter(Boolean)
                            if (!currentTags.includes(tag)) {
                                setTags(tags ? `${tags}, ${tag}` : tag)
                            }
                        }}
                    >
                        {tag}
                    </Badge>
                ))}
            </div>
            <div className="grid grid-cols-4 items-start gap-4">
                <Label htmlFor="subtasks" className="text-right text-sm">Subtasks</Label>
                <div className="col-span-3 space-y-2">
                    <div className="flex items-center gap-2">
                        <Input
                            id="new-subtask"
                            value={newSubtask}
                            onChange={(e) => setNewSubtask(e.target.value)}
                            onKeyDown={handleKeyDown}
                            placeholder="Add a subtask..."
                            className="flex-1"
                        />
                        <Button type="button" onClick={handleAddSubtask} size="sm" disabled={!newSubtask.trim()}>
                            <Plus className="h-4 w-4" />
                            Add
                        </Button>
                    </div>
                    {subtasks.length > 0 ? (
                        <div className="space-y-2 mt-2">
                            {subtasks.map((subtask) => (
                                <div key={subtask.id} className="flex items-center gap-2 group">
                                    <Checkbox
                                        id={subtask.id}
                                        checked={subtask.completed}
                                        onCheckedChange={() => handleToggleSubtask(subtask.id)}
                                    />
                                    <Label
                                        htmlFor={subtask.id}
                                        className={cn(
                                            "text-sm flex-1 cursor-pointer",
                                            subtask.completed && "line-through text-muted-foreground",
                                        )}
                                    >
                                        {subtask.title}
                                    </Label>
                                    <Button
                                        variant="ghost"
                                        size="icon"
                                        className="h-6 w-6 opacity-0 group-hover:opacity-100 transition-opacity"
                                        onClick={() => handleDeleteSubtask(subtask.id)}
                                    >
                                        <Trash2 className="h-4 w-4 text-muted-foreground" />
                                    </Button>
                                </div>
                            ))}
                        </div>
                    ) : (
                        <div className="text-sm text-muted-foreground italic">No subtasks added yet</div>
                    )}
                </div>
            </div>
          </div>
          <DialogFooter>
            <Button type="button" variant="outline" onClick={() => { resetForm(); onOpenChange(false); }}>
              Cancel
            </Button>
            <Button type="submit">Create Card</Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}