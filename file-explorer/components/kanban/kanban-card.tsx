"use client"

import React, { useState, useMemo } from "react"
import { useDraggable } from "@dnd-kit/core"
import { Card as CardContextType, CardType } from "./board-context"
import { Badge } from "@/components/ui/badge"
import { CSS } from "@dnd-kit/utilities"
import { CardDetailView } from "./card-detail-view" // Moved
import { Card as UICard, CardContent } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { Activity, CalendarIcon, Clock, Link, MoreHorizontal, GripVertical, Network, Settings } from "lucide-react"
import { formatDistanceToNow } from "date-fns"

interface KanbanCardProps {
  card: CardContextType
  cardTypes: CardType[]
  isDragOverlay?: boolean
  columnId: string
  onCardUpdate?: (updatedCard: CardContextType) => void
  onDeleteCard?: (cardId: string) => void
}

export function KanbanCard({
  card,
  cardTypes,
  isDragOverlay,
  columnId,
  onCardUpdate,
  onDeleteCard,
}: KanbanCardProps) {
  const [isDetailViewOpen, setIsDetailViewOpen] = useState(false)

  const { attributes, listeners, setNodeRef, transform, isDragging: dndIsDragging } = useDraggable({
    id: card.id,
    data: {
      type: "card",
      card: card,
      columnId: columnId,
    },
    disabled: isDragOverlay,
  })

  const style = !isDragOverlay
    ? {
        transform: transform ? CSS.Transform.toString(transform) : undefined,
        opacity: dndIsDragging ? 0.8 : 1,
        zIndex: dndIsDragging ? 1000 : undefined,
        position: dndIsDragging ? ("relative" as const) : undefined,
        // Ensure the card remains interactive
        pointerEvents: "auto" as const,
        cursor: dndIsDragging ? "grabbing" : "pointer"
      }
    : {}

  const cardTypeDetails = useMemo(() => {
    const byId = cardTypes.find((ct) => ct.id === card.priority)
    if (byId) return byId
    const byName = cardTypes.find((ct) => ct.name.toLowerCase() === (card.priority || "").toLowerCase())
    if (byName) return byName
    return cardTypes.length > 0 ? cardTypes[0] : { id: "default", name: "Default", color: "#888888" };
  }, [card.priority, cardTypes])

  const cardTypeColorStyle = cardTypeDetails ? { borderLeft: `4px solid ${cardTypeDetails.color}` } : {}

  const handleOpenDetailView = (e: React.MouseEvent) => {
    // Don't open detail view if we're in a drag operation
    if (isDragOverlay || dndIsDragging) return;

    // Don't open if clicking on the drag handle
    const target = e.target as HTMLElement;
    if (target.closest('[aria-label="Drag card"]')) {
        return;
    }

    // Don't open if clicking on the more options button
    if (target.closest('[aria-label="More options"]')) {
        return;
    }

    // Otherwise, open the detail view
    setIsDetailViewOpen(true);

    // Ensure event doesn't propagate further to prevent unintended side effects
    e.stopPropagation();
  }

  const handleCardUpdateFromDetail = (updatedCard: CardContextType) => {
    if (onCardUpdate) {
      onCardUpdate(updatedCard)
    }
    setIsDetailViewOpen(false)
  }

  const handleDeleteCardFromDetail = () => {
    if (onDeleteCard) {
        onDeleteCard(card.id)
    }
    setIsDetailViewOpen(false)
  }

  const formatRelativeDate = (dateString?: string) => {
    if (!dateString) return null
    try {
      const date = new Date(dateString)
      return formatDistanceToNow(date, { addSuffix: true })
    } catch (error) {
      console.error("Error formatting date:", error)
      return dateString
    }
  }

  const hasDependencies = card.dependencies && card.dependencies.length > 0;

  const cardDisplayContent = (
    <UICard
      className={`shadow-sm transition-all duration-200 w-full overflow-hidden ${
        dndIsDragging || isDragOverlay ? "opacity-90 border-primary/50" : ""
      }`}
    >
      <CardContent className="p-3 relative overflow-hidden">
        {!isDragOverlay && (
          <div
            {...attributes}
            {...listeners}
            className="absolute top-1 right-1 p-1.5 rounded-full text-muted-foreground hover:bg-muted hover:text-foreground focus:outline-none focus:ring-1 focus:ring-ring cursor-grab active:cursor-grabbing"
            onClick={(e) => e.stopPropagation()}
            aria-label="Drag card"
          >
            <GripVertical className="h-4 w-4" />
          </div>
        )}

        <div className="flex justify-between items-start mb-1 pr-7">
          <h3 className="font-medium text-sm line-clamp-2 mr-2 overflow-hidden text-ellipsis">{card.title}</h3>
          <button
            onClick={(e) => { e.stopPropagation(); setIsDetailViewOpen(true); }}
            className="text-muted-foreground hover:text-foreground p-0.5 rounded hover:bg-muted"
            aria-label="More options"
          >
            <MoreHorizontal className="h-4 w-4" />
          </button>
        </div>

        {card.projectId && <div className="text-xs text-muted-foreground mb-2">{card.projectId}</div>}

        {card.description && (
          <div className="text-xs text-muted-foreground mb-2 line-clamp-2 overflow-hidden text-ellipsis">
            {card.description}
          </div>
        )}

        {hasDependencies && (
          <div className="mb-2 flex items-center gap-1 bg-muted/50 p-1 rounded-sm text-xs">
            <Link className="h-3 w-3 text-muted-foreground" />
            <span className="text-muted-foreground">
              {card.dependencies?.length} {card.dependencies?.length === 1 ? "dependency" : "dependencies"}
            </span>
          </div>
        )}

        {card.resourceMetrics && (
          <div className="mb-2 bg-muted/40 p-1.5 rounded-sm text-xs">
            <div className="flex justify-between items-center gap-2">
              <span className="flex items-center">
                <TooltipProvider><Tooltip><TooltipTrigger asChild><Activity className="h-3 w-3 mr-0.5 text-blue-500" /></TooltipTrigger><TooltipContent>CPU Time</TooltipContent></Tooltip></TooltipProvider>
                {card.resourceMetrics.cpuTime || 0}%
              </span>
              <span className="flex items-center">
                 <TooltipProvider><Tooltip><TooltipTrigger asChild><Network className="h-3 w-3 mr-0.5 text-green-500" /></TooltipTrigger><TooltipContent>Memory Usage</TooltipContent></Tooltip></TooltipProvider>
                {card.resourceMetrics.memoryUsage || 0}MB
              </span>
              <span className="flex items-center">
                <TooltipProvider><Tooltip><TooltipTrigger asChild><Settings className="h-3 w-3 mr-0.5 text-purple-500" /></TooltipTrigger><TooltipContent>Token Usage</TooltipContent></Tooltip></TooltipProvider>
                {card.resourceMetrics.tokenUsage || 0}k
              </span>
            </div>
          </div>
        )}

        <div className="flex flex-wrap gap-1 mb-2">
          {cardTypeDetails && (
            <Badge
              variant="outline"
              className="text-[10px] px-1.5 py-0.5 h-5 font-normal"
              style={{
                backgroundColor: `${cardTypeDetails.color}20`,
                color: cardTypeDetails.color,
                borderColor: `${cardTypeDetails.color}80`,
              }}
            >
              {cardTypeDetails.name}
            </Badge>
          )}
          {card.tags &&
            card.tags.map((tag) => (
              <Badge key={tag} variant="secondary" className="text-[10px] px-1.5 py-0.5 h-5 font-normal">
                {tag}
              </Badge>
            ))}
        </div>

        {typeof card.progress === "number" && (
          <div className="space-y-1 mb-2">
            <div className="flex justify-between text-xs text-muted-foreground">
              <span>Progress</span>
              <span>{card.progress}%</span>
            </div>
            <Progress value={card.progress} className="h-1" />
          </div>
        )}

        <div className="flex justify-between items-center text-xs text-muted-foreground mt-2">
          <div className="flex -space-x-2">
            {card.agentAssignments &&
              card.agentAssignments.slice(0, 2).map((assignment) => (
                <TooltipProvider key={assignment.agentId}>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Avatar className="h-5 w-5 border-2 border-background">
                        <AvatarFallback className="text-[9px]">
                          {assignment.agentType.substring(0, 1).toUpperCase()}
                        </AvatarFallback>
                      </Avatar>
                    </TooltipTrigger>
                    <TooltipContent><p>{assignment.agentType}</p></TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              ))}
          </div>

          {card.storyPoints && (
             <TooltipProvider><Tooltip>
                <TooltipTrigger asChild>
                    <Badge variant="outline" className="text-[10px] px-1.5 py-0.5 h-5 font-medium">
                        {card.storyPoints} SP
                    </Badge>
                </TooltipTrigger>
                <TooltipContent>Story Points</TooltipContent>
            </Tooltip></TooltipProvider>
          )}

          {card.dueDate && (
            <div className="flex items-center">
              <CalendarIcon className="h-3 w-3 mr-1" />
              {formatRelativeDate(card.dueDate)}
            </div>
          )}
        </div>

        {card.subtasks && card.subtasks.length > 0 && (
          <div className="mt-2">
            <div className="flex justify-between text-xs text-muted-foreground mb-0.5">
              <span>Subtasks</span>
              <span>
                {card.subtasks.filter((st) => st.completed).length} / {card.subtasks.length}
              </span>
            </div>
            <Progress
              value={(card.subtasks.filter((st) => st.completed).length / card.subtasks.length) * 100}
              className="h-1"
            />
          </div>
        )}

        {card.updatedAt && (
          <div className="flex justify-end mt-2">
            <div className="flex items-center text-xs text-muted-foreground">
              <Clock className="h-3 w-3 mr-1" />
              {formatRelativeDate(card.updatedAt)}
            </div>
          </div>
        )}
      </CardContent>
    </UICard>
  );

  if (isDragOverlay) {
    return <div style={{...cardTypeColorStyle, width: '270px',  boxSizing: 'border-box'}} className="rounded-md max-w-full shadow-xl bg-card">{cardDisplayContent}</div>;
  }

  return (
    <>
      <div
        ref={setNodeRef}
        style={{ ...style, ...cardTypeColorStyle }}
        className={`mb-2 transition-shadow duration-200 rounded-md max-w-full ${
           dndIsDragging ? "shadow-xl cursor-grabbing" : "hover:shadow-lg hover:-translate-y-0.5 cursor-pointer"
        }`}
        onClick={handleOpenDetailView}
        // Ensure this element can be clicked
        role="button"
        tabIndex={0}
        aria-label={`Card: ${card.title}`}
        // Add keyboard accessibility
        onKeyDown={(e) => {
          if (e.key === 'Enter' || e.key === ' ') {
            e.preventDefault();
            handleOpenDetailView(e as unknown as React.MouseEvent);
          }
        }}
      >
        {cardDisplayContent}
      </div>

      {!isDragOverlay && isDetailViewOpen && (
        <CardDetailView
          card={card}
          cardTypes={cardTypes}
          onClose={() => setIsDetailViewOpen(false)}
          onUpdate={handleCardUpdateFromDetail}
          onDelete={handleDeleteCardFromDetail}
        />
      )}
    </>
  )
}