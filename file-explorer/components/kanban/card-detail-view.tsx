"use client"

import React, { useState, useEffect } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON>Tit<PERSON>, Di<PERSON>Footer } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { Calendar } from "@/components/ui/calendar"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { CalendarIcon, Plus, Trash2, Link, CornerDownRight, Clock } from "lucide-react"
import { format, formatDistanceToNow } from "date-fns"
import { cn } from "@/lib/utils"
import { Checkbox } from "@/components/ui/checkbox"
import { Badge } from "@/components/ui/badge"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Card, CardType, AgentAssignment, TaskHistoryItem } from "./board-context" // Assuming types from board-context

interface CardDetailViewProps {
  card: Card
  cardTypes: CardType[]
  onClose: () => void
  onUpdate: (updatedCard: Card) => void
  onDelete: () => void
}

export function CardDetailView({ card, cardTypes, onClose, onUpdate, onDelete }: CardDetailViewProps) {
  const [localCard, setLocalCard] = useState<Card>(card)
  const [newSubtaskTitle, setNewSubtaskTitle] = useState("")
  const [activeTab, setActiveTab] = useState("details")

  useEffect(() => {
    setLocalCard(card)
  }, [card])

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { id, value } = e.target
    setLocalCard((prev) => ({ ...prev, [id]: value }))
  }

  const handleDateChange = (date: Date | undefined) => {
    setLocalCard((prev) => ({ ...prev, dueDate: date ? date.toISOString() : undefined }))
  }

  const handlePriorityChange = (value: string) => {
    setLocalCard((prev) => ({ ...prev, priority: value }))
  }

  const handleTagsChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setLocalCard((prev) => ({
      ...prev,
      tags: e.target.value.split(",").map((tag) => tag.trim()).filter(Boolean),
    }))
  }

  const handleAddSubtask = () => {
    if (newSubtaskTitle.trim()) {
      setLocalCard((prev) => ({
        ...prev,
        subtasks: [
          ...(prev.subtasks || []),
          { id: `subtask-${Date.now()}`, title: newSubtaskTitle.trim(), completed: false },
        ],
      }))
      setNewSubtaskTitle("")
    }
  }

  const handleToggleSubtask = (id: string) => {
    setLocalCard((prev) => ({
      ...prev,
      subtasks: (prev.subtasks || []).map((subtask) =>
        subtask.id === id ? { ...subtask, completed: !subtask.completed } : subtask
      ),
    }))
  }

  const handleDeleteSubtask = (id: string) => {
    setLocalCard((prev) => ({
      ...prev,
      subtasks: (prev.subtasks || []).filter((subtask) => subtask.id !== id),
    }))
  }

  const handleSave = () => {
    onUpdate(localCard)
  }

  const formatRelativeDate = (dateString?: string) => {
    if (!dateString) return null
    try {
      const date = new Date(dateString)
      return formatDistanceToNow(date, { addSuffix: true })
    } catch (error) {
      console.error("Error formatting date:", error)
      return dateString
    }
  }

  const getPriorityColor = (priorityId: string) => {
    const type = cardTypes.find(ct => ct.id === priorityId);
    return type ? type.color : "#888888"; // Default gray if not found
  }

  return (
    <Dialog open={true} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[700px] max-h-[90vh] flex flex-col">
        <DialogHeader>
          <DialogTitle>Card Details</DialogTitle>
        </DialogHeader>
        <div className="flex-1 overflow-hidden grid grid-cols-[2fr_1fr] gap-4">
          {/* Left Pane: Details, Description, Subtasks, Tags */}
          <ScrollArea className="pr-4">
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="title">Title</Label>
                <Input id="title" value={localCard.title} onChange={handleInputChange} />
              </div>
              <div className="space-y-2">
                <Label htmlFor="description">Description</Label>
                <Textarea id="description" value={localCard.description} onChange={handleInputChange} rows={5} />
              </div>

              {/* Subtasks Section */}
              <div className="space-y-2">
                <Label htmlFor="subtasks">Subtasks</Label>
                <div className="flex items-center gap-2">
                  <Input
                    id="new-subtask-title"
                    value={newSubtaskTitle}
                    onChange={(e) => setNewSubtaskTitle(e.target.value)}
                    placeholder="Add a subtask..."
                  />
                  <Button type="button" onClick={handleAddSubtask} size="sm">
                    <Plus className="h-4 w-4" />
                  </Button>
                </div>
                <div className="space-y-2">
                  {localCard.subtasks?.map((subtask) => (
                    <div key={subtask.id} className="flex items-center gap-2">
                      <Checkbox
                        id={`subtask-${subtask.id}`}
                        checked={subtask.completed}
                        onCheckedChange={() => handleToggleSubtask(subtask.id)}
                      />
                      <Label
                        htmlFor={`subtask-${subtask.id}`}
                        className={cn("flex-1", subtask.completed && "line-through text-muted-foreground")}
                      >
                        {subtask.title}
                      </Label>
                      <Button variant="ghost" size="icon" onClick={() => handleDeleteSubtask(subtask.id)}>
                        <Trash2 className="h-4 w-4 text-muted-foreground" />
                      </Button>
                    </div>
                  ))}
                </div>
              </div>

              {/* Dependencies */}
              <div className="space-y-2">
                <Label>Dependencies</Label>
                {localCard.dependencies?.length > 0 ? (
                  <div className="space-y-1">
                    {localCard.dependencies.map((depId) => (
                      <Badge key={depId} variant="outline" className="flex items-center justify-between">
                        <Link className="h-3 w-3 mr-1" />
                        {depId}
                        <Button variant="ghost" size="icon" className="h-4 w-4 ml-2" onClick={() => { /* Remove dependency */ }}>
                          <X className="h-3 w-3" />
                        </Button>
                      </Badge>
                    ))}
                  </div>
                ) : (
                  <p className="text-sm text-muted-foreground italic">No dependencies</p>
                )}
                {/* Add functionality to add dependencies */}
              </div>
            </div>
          </ScrollArea>

          {/* Right Pane: Metadata, History */}
          <ScrollArea className="pl-4 border-l">
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="priority">Priority</Label>
                <select
                  id="priority"
                  value={localCard.priority}
                  onChange={handlePriorityChange}
                  className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm"
                  style={{borderLeft: `4px solid ${getPriorityColor(localCard.priority)}`}}
                >
                  {cardTypes.map((type) => (
                    <option key={type.id} value={type.id}>
                      {type.name}
                    </option>
                  ))}
                </select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="projectId">Project ID</Label>
                <Input id="projectId" value={localCard.projectId || ''} onChange={handleInputChange} />
              </div>
              <div className="space-y-2">
                <Label htmlFor="dueDate">Due Date</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className={cn("w-full justify-start text-left font-normal", !localCard.dueDate && "text-muted-foreground")}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {localCard.dueDate ? format(new Date(localCard.dueDate), "PPP") : "Select a date"}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar
                      mode="single"
                      selected={localCard.dueDate ? new Date(localCard.dueDate) : undefined}
                      onSelect={handleDateChange}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>
              <div className="space-y-2">
                <Label htmlFor="assignee">Assignee</Label>
                <Input id="assignee" value={localCard.assignee || ''} onChange={handleInputChange} />
              </div>
              <div className="space-y-2">
                <Label htmlFor="tags">Tags</Label>
                <Input id="tags" value={(localCard.tags || []).join(", ")} onChange={handleTagsChange} placeholder="Comma-separated tags" />
              </div>
              <div className="space-y-2">
                <Label>Progress</Label>
                <Input
                  type="number"
                  value={localCard.progress}
                  onChange={(e) => setLocalCard((prev) => ({ ...prev, progress: Math.min(100, Math.max(0, Number(e.target.value))) }))}
                  min={0}
                  max={100}
                />
              </div>

              {/* Task History */}
              <div className="space-y-2">
                <Label>Task History</Label>
                <div className="space-y-1">
                  {localCard.taskHistory.length > 0 ? (
                    localCard.taskHistory.map((item: TaskHistoryItem, index: number) => (
                      <div key={index} className="flex items-start text-xs text-muted-foreground">
                        <Clock className="h-3 w-3 mr-1 mt-0.5" />
                        <div className="flex-1">
                          <p>
                            <strong>{item.action}</strong> by {item.agentId}
                          </p>
                          <p className="ml-4">{item.details}</p>
                          <p className="ml-4 italic">{formatRelativeDate(item.timestamp)}</p>
                        </div>
                      </div>
                    ))
                  ) : (
                    <p className="text-sm text-muted-foreground italic">No history yet</p>
                  )}
                </div>
              </div>
            </div>
          </ScrollArea>
        </div>
        <DialogFooter className="mt-4">
          <Button variant="destructive" onClick={onDelete}>Delete Card</Button>
          <Button variant="outline" onClick={onClose}>Cancel</Button>
          <Button onClick={handleSave}>Save Changes</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}