"use client"

import { useState, useEffect } from "react"
import { useRout<PERSON> } from "next/navigation"
import { <PERSON>alog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import { useBoard } from "./board-context"
import { useToast } from "@/components/ui/use-toast" // Use common useToast path

interface BoardSettingsDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  boardId: string
}

export function BoardSettingsDialog({ open, onOpenChange, boardId }: BoardSettingsDialogProps) {
  const { boards, activeBoard, updateBoard, deleteBoard } = useBoard()
  const [boardName, setBoardName] = useState("")
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const { toast } = useToast(); // Initialize toast
  const router = useRouter()

  // Update board name when active board changes
  useEffect(() => {
    if (open && boardId) {
      // Prefer using the boardId prop to find the specific board being edited
      const boardToEdit = boards.find((b) => b.id === boardId)
      if (boardToEdit) {
        setBoardName(boardToEdit.name)
      } else if (activeBoard && activeBoard.id === boardId) {
        // Fallback to activeBoard (though boardId prop is preferred)
        setBoardName(activeBoard.name)
      }
    }
  }, [open, boardId, boards, activeBoard])

  const handleSave = () => {
    if (boardName.trim()) {
      updateBoard(boardId, boardName)
      onOpenChange(false)
      toast({
        title: "Board settings saved.",
        description: `Board "${boardName}" settings have been updated.`,
      });
    }
  }

  const handleDeleteConfirm = () => {
    setIsDeleteDialogOpen(false)
    onOpenChange(false)
    deleteBoard(boardId)

    // Navigate to the first available board
    const firstBoard = boards.find((b) => b.id !== boardId)
    if (firstBoard) {
      router.push(`/?board=${firstBoard.id}`)
    } else {
      router.push("/")
    }
    toast({
      title: "Board deleted.",
      description: `Board "${boardName}" has been permanently deleted.`,
      variant: "destructive",
    });
  }

  const handleExportBoard = () => {
    // Find the board to export
    const boardToExport = boards.find((b) => b.id === boardId) || activeBoard
    if (!boardToExport) {
        toast({
            title: "Export failed.",
            description: "No board found to export.",
            variant: "destructive",
        });
        return;
    }

    // Create a JSON representation of the board
    const boardDataToExport = {
      ...boardToExport,
      exportDate: new Date().toISOString(),
    }

    const dataStr = JSON.stringify(boardDataToExport, null, 2)
    const dataUri = "data:application/json;charset=utf-8," + encodeURIComponent(dataStr)

    const exportFileDefaultName = `kanban-board-${boardId}-${new Date().toLocaleDateString()}.json`

    try {
        const linkElement = document.createElement("a")
        linkElement.setAttribute("href", dataUri)
        linkElement.setAttribute("download", exportFileDefaultName)
        document.body.appendChild(linkElement); // Required for Firefox
        linkElement.click()
        document.body.removeChild(linkElement); // Clean up
        
        toast({
            title: "Board exported!",
            description: `Board data saved as "${exportFileDefaultName}".`,
        });
        onOpenChange(false);
    } catch (error) {
        console.error("Error exporting board:", error);
        toast({
            title: "Export failed.",
            description: "Could not export board data.",
            variant: "destructive",
        });
    }
  }

  return (
    <>
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Board Settings</DialogTitle>
            <DialogDescription>Manage your board settings and preferences.</DialogDescription>
          </DialogHeader>

          <Tabs defaultValue="general">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="general">General</TabsTrigger>
              <TabsTrigger value="export">Export</TabsTrigger>
              <TabsTrigger value="danger">Danger Zone</TabsTrigger>
            </TabsList>

            <TabsContent value="general" className="space-y-4 py-4">
              <div className="grid gap-2">
                <Label htmlFor="board-name">Board Name</Label>
                <Input
                  id="board-name"
                  value={boardName}
                  onChange={(e) => setBoardName(e.target.value)}
                  placeholder="My Board"
                />
              </div>

              <div className="flex justify-end">
                <Button onClick={handleSave} disabled={!boardName.trim()}>
                  Save Changes
                </Button>
              </div>
            </TabsContent>

            <TabsContent value="export" className="space-y-4 py-4">
              <div className="grid gap-2">
                <h3 className="text-sm font-medium">Export Board Data</h3>
                <p className="text-sm text-muted-foreground">
                  Export your board data as a JSON file that you can import later or use in other applications.
                </p>
              </div>

              <div className="flex justify-end">
                <Button onClick={handleExportBoard}>Export as JSON</Button>
              </div>
            </TabsContent>

            <TabsContent value="danger" className="space-y-4 py-4">
              <div className="grid gap-2">
                <h3 className="text-sm font-medium text-destructive">Delete Board</h3>
                <p className="text-sm text-muted-foreground">
                  Permanently delete this board and all of its data. This action cannot be undone.
                </p>
              </div>

              <div className="flex justify-end">
                <Button variant="destructive" onClick={() => setIsDeleteDialogOpen(true)}>
                  Delete Board
                </Button>
              </div>
            </TabsContent>
          </Tabs>
        </DialogContent>
      </Dialog>

      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This will permanently delete your board and all of its data. This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteConfirm}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  )
}