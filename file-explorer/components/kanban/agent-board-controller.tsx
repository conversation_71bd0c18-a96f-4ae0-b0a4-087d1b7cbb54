"use client"

import type React from "react"
import { createContext, useContext, useState, useEffect, useRef } from "react"
import { useBoard, Card, Column, Swimlane, CardType } from "./board-context"
import { useToast } from "@/components/ui/use-toast" // Corrected import path for useToast
import { CompleteAgentManager } from "../agents/agent-manager-complete" // Adjust path as per file_map
import { AgentMessage, AgentStatus as AgentStatusType } from "../agents/agent-base" // Import shared AgentMessage and AgentStatus

// Define an interface for board interactions that CompleteAgentManager will use
// This ensures CompleteAgentManager doesn't directly depend on React context
interface IBoardAgentService {
  createTaskCard(task: any, agentId: string): Promise<any>;
  moveCardToColumn(cardId: string, columnId: string, agentId: string): Promise<any>;
  addCardDependency(cardId: string, dependencyCardId: string, agentId: string): Promise<any>;
  updateCardProgress(cardId: string, progress: number, agentId: string): Promise<any>; // Added updateCardProgress
}

// Agent types (use the shared AgentStatusType from agent-base)
export type AgentStatus = AgentStatusType['status']; // Narrow down for local use if preferred
export type AgentType = AgentStatusType['type']; // Use directly from shared type

// Re-exporting Agent type for consistency within kanban components
export type Agent = AgentStatusType;

type AgentBoardControllerContextType = {
  agents: Agent[]; // Use the consolidated Agent type
  isAgentSystemActive: boolean
  startAgent: () => void
  stopAgent: () => void
  agentLogs: string[]
  clearLogs: () => void
  agentStatus: "idle" | "running" | "paused" | "error"
  // If needed, can expose agentManager for advanced operations in the UI
  // agentManager: CompleteAgentManager | null;
}

// Initial agents - now just a placeholder for the context provider, actual agents loaded by manager
const initialAgents: Agent[] = [];

const AgentBoardControllerContext = createContext<AgentBoardControllerContextType | undefined>(undefined)

export function AgentBoardControllerProvider({ children }: { children: React.ReactNode }) {
  const [agents, setAgents] = useState<Agent[]>(initialAgents)
  const [isAgentSystemActive, setIsAgentSystemActive] = useState(false)
  const [agentLogs, setAgentLogs] = useState<string[]>([])
  const [agentStatus, setAgentStatus] = useState<"idle" | "running" | "paused" | "error">("idle")

  const { activeBoard, addCardToColumn, updateCardInColumn, updateAgents } = useBoard()
  const { toast } = useToast()

  // Use refs to persist instances of CompleteAgentManager and BoardAgentAPI across renders
  const agentManagerRef = useRef<CompleteAgentManager | null>(null);
  const boardAgentAPIRef = useRef<any | null>(null); // Use 'any' to avoid circular dependency with BoardAgentAPI's type

  // Effect to initialize the AgentManagerComplete and BoardAgentAPI
  useEffect(() => {
    // Only initialize if activeBoard is available
    if (!activeBoard) {
        console.warn("No active board, Agent Manager and Board Agent API cannot be initialized.");
        setIsAgentSystemActive(false);
        setAgentStatus("idle");
        if (agentManagerRef.current) {
          // agentManagerRef.current.shutdown(); // Consider if shutdown is needed on board change vs unmount
          // agentManagerRef.current = null;
        }
        return;
    }

    // Dynamic import for BoardAgentAPI to break potential circular dependency in type declarations
    // when BoardAgentAPI imports Card, Column etc. from board-context, and agent-board-controller imports BoardAgentAPI.
    const importBoardApi = async () => {
      try {
        const { BoardAgentAPI } = await import("../../lib/board-agent-api"); // Corrected dynamic import path
        
        // Adapter object to bridge useBoard() methods with BoardAgentAPI's expected `boardContext`
        const boardContextAdapter = {
            addCard: (newCard: Card) => {
                if (!activeBoard || !newCard.columnId) {
                    console.error("Attempted to add card without activeBoard or columnId in adapter.");
                    return;
                }
                return addCardToColumn(activeBoard.id, newCard.columnId, newCard);
            },
            updateCard: (updatedCard: Card) => {
                if (!activeBoard || !updatedCard.columnId) { // Ensure updatedCard has columnId for consistency
                    console.error("Attempted to update card without activeBoard or columnId in adapter.");
                    return;
                }
                // Call updateCardInColumn which expects boardId, columnId, and the card itself
                // The `BoardAgentAPI`'s `updateCard` passes a full card, so we ensure columnId is present on it.
                return updateCardInColumn(activeBoard.id, updatedCard); // Passing updatedCard directly as per board-context update
            },
            // The following getters are needed by BoardAgentAPI's internal `getAllCards` and `getCardById`
            get columns() { return activeBoard.columns; },
            get swimlanes() { return activeBoard.swimlanes; },
        };

        const currentBoardAgentAPI = new BoardAgentAPI(boardContextAdapter);
        boardAgentAPIRef.current = currentBoardAgentAPI;

        // Implement IBoardAgentService using the BoardAgentAPI instance
        const boardServiceForAgentManager: IBoardAgentService = {
            createTaskCard: async (task: any, agentId: string) => {
                return await currentBoardAgentAPI.createTaskCard(task, agentId);
            },
            moveCardToColumn: async (cardId: string, columnId: string, agentId: string) => {
                return await currentBoardAgentAPI.moveCardToColumn(cardId, columnId, agentId);
            },
            addCardDependency: async (cardId: string, dependencyCardId: string, agentId: string) => {
                return await currentBoardAgentAPI.addCardDependency(cardId, dependencyCardId, agentId);
            },
            updateCardProgress: async (cardId: string, progress: number, agentId: string) => {
                return await currentBoardAgentAPI.updateCardProgress(cardId, progress, agentId);
            },
        };

        // Initialize CompleteAgentManager only once per session (or when board changes significantly)
        if (!agentManagerRef.current) {
          console.log("Initializing CompleteAgentManager...");
          const manager = new CompleteAgentManager(boardServiceForAgentManager);
          agentManagerRef.current = manager;

          // Subscribe to agent manager messages for logging/status updates
          manager.onMessage((message: AgentMessage) => {
            const timestamp = new Date(message.timestamp).toLocaleTimeString();
            setAgentLogs((prev) => [...prev, `[${timestamp}] [${message.agentId}] ${message.message}`]);
            // Re-fetch and update all agent statuses to ensure UI reflects changes
            const updatedStatuses = manager.getAllAgentStatuses();
            const mappedAgents: Agent[] = updatedStatuses.map(s => ({
              id: s.id,
              name: s.name,
              type: s.type,
              status: s.status,
              currentTaskId: s.currentTask,
              capabilities: s.capabilities,
              healthScore: s.healthScore, // Add healthScore directly from AgentStatus
              tokensUsed: s.tokensUsed, // Add tokensUsed directly from AgentStatus
              resourceUsage: { // Map to resourceUsage for component compatibility
                cpu: s.healthScore,
                memory: s.tokensUsed / 1000,
                tokens: s.tokensUsed,
              },
            }));
            updateAgents(activeBoard.id, mappedAgents);
          });
        }
      } catch (error) {
        console.error("Failed to dynamically import BoardAgentAPI:", error);
        toast({
            title: "Initialization Error",
            description: "Failed to load board API for agent system.",
            variant: "destructive",
        });
      }
    };

    importBoardApi();
    

    // Update agent statuses periodically from the manager for continuous display
    const interval = setInterval(() => {
      if (agentManagerRef.current && activeBoard) {
        const updatedStatuses = agentManagerRef.current.getAllAgentStatuses();
        const mappedAgents: Agent[] = updatedStatuses.map(s => ({
          id: s.id,
          name: s.name,
          type: s.type,
          status: s.status,
          currentTaskId: s.currentTask,
          capabilities: s.capabilities,
          healthScore: s.healthScore,
          tokensUsed: s.tokensUsed,
          resourceUsage: {
            cpu: s.healthScore,
            memory: s.tokensUsed / 1000,
            tokens: s.tokensUsed,
          },
        }));
        updateAgents(activeBoard.id, mappedAgents);

        // Update overall agent system status based on Micromanager's status
        const micromanagerStatus = updatedStatuses.find(a => a.id === 'micromanager')?.status;
        if (micromanagerStatus === 'busy' || micromanagerStatus === 'error' || updatedStatuses.some(a => a.status === 'busy' || a.status === 'error')) {
          setIsAgentSystemActive(true); // Agent system is active if any agent is busy or in error
        } else {
          setIsAgentSystemActive(false);
        }

        if (micromanagerStatus === 'busy') setAgentStatus('running');
        else if (micromanagerStatus === 'error') setAgentStatus('error');
        else if (updatedStatuses.some(a => a.status === 'busy')) setAgentStatus('running'); // Any agent running
        else setAgentStatus('idle');
      }
    }, 5000); // Update every 5 seconds

    // Cleanup function when component unmounts or activeBoard changes
    return () => {
      // It's tricky to shutdown the manager on every board change if it's meant to be long-lived.
      // For now, only clear interval, and let the manager persist until full app shutdown.
      // If a new manager is needed per board, this logic would need to be more complex.
      clearInterval(interval);
      if (agentManagerRef.current) { // Only shut down manager if board is truly unmounted
         agentManagerRef.current.offMessage(() => {}); // Deregister all listeners
         agentManagerRef.current.shutdown(); // Ensure proper shutdown
         agentManagerRef.current = null;
      }
    };
  }, [activeBoard, addCardToColumn, updateCardInColumn, updateAgents]); // Re-run if activeBoard or its methods change

  const startAgent = async () => {
    if (!activeBoard || !agentManagerRef.current) {
      toast({
        title: "Error",
        description: "Agent system not fully initialized or no active board.",
        variant: "destructive",
      })
      return
    }

    // Set UI status immediately
    setIsAgentSystemActive(true)
    setAgentStatus("running")

    const timestamp = new Date().toISOString()
    setAgentLogs((prev) => [...prev, `[${timestamp}] Agent system started. Submitting initial task to Micromanager.`])

    toast({
      title: "Agent system started",
      description: "The AI agent system is now running and awaiting tasks.",
    })

    // Example: Submit an initial task to the micromanager
    // In a real application, this task would likely come from user input via a dedicated UI.
    try {
        const initialTask = "Develop a simple user authentication module with sign-up and login functionality. Include database schema, API endpoints, and a basic frontend component.";
        setAgentLogs((prev) => [...prev, `[${new Date().toISOString()}] User task submitted: "${initialTask}"`]);
        const newTaskId = await agentManagerRef.current.submitTask(initialTask, undefined, 'urgent'); // originalTaskId handled internally now
        setAgentLogs((prev) => [...prev, `[${new Date().toISOString()}] Task submitted to Micromanager with internal ID: ${newTaskId}.`]);
    } catch (error: any) {
        console.error("Error submitting initial task to agent manager:", error);
        setAgentLogs((prev) => [...prev, `[${new Date().toISOString()}] Error submitting task: ${error.message || String(error)}`]);
        setAgentStatus("error");
        toast({
            title: "Agent Error",
            description: `Failed to submit initial task: ${error.message || String(error)}`,
            variant: "destructive",
        });
        setIsAgentSystemActive(false); // Stop if initial task submission fails
    }
  }

  const stopAgent = () => {
    if (agentManagerRef.current) {
      agentManagerRef.current.shutdown(); // Call shutdown on the manager
    }
    setIsAgentSystemActive(false);
    setAgentStatus("idle");
    const timestamp = new Date().toISOString();
    setAgentLogs((prev) => [...prev, `[${timestamp}] Agent system stopped`]);
    toast({
      title: "Agent system stopped",
      description: "The AI agent system has been stopped.",
    });
  };

  const clearLogs = () => {
    setAgentLogs([])
  }

  // Placeholder functions for now, as direct manipulation from UI is not the primary goal
  // These will be updated as needed in future iterations if UI controls are added
  const registerAgent = (agent: Agent) => { console.warn("registerAgent not fully implemented through UI"); };
  const removeAgent = (agentId: string) => { console.warn("removeAgent not fully implemented through UI"); };
  const assignTaskToAgent = (agentId: string, taskId: string) => { console.warn("assignTaskToAgent not fully implemented through UI"); };
  const unassignTaskFromAgent = (agentId: string) => { console.warn("unassignTaskFromAgent not fully implemented through UI"); };
  const updateAgentStatus = (agentId: string, status: AgentStatus, taskId?: string) => { console.warn("updateAgentStatus not fully implemented through UI"); };
  const updateAgentResourceUsage = (agentId: string, resourceUsage: Partial<Agent["resourceUsage"]>) => { console.warn("updateAgentResourceUsage not fully implemented through UI"); };
  const getAgentById = (agentId: string) => { return agents.find(a => a.id === agentId); }; // This can use local state
  const getAgentsByType = (type: AgentType) => { return agents.filter(a => a.type === type); }; // This can use local state
  const getAgentsByStatus = (status: AgentStatus) => { return agents.filter(a => a.status === status); }; // This can use local state
  const getAgentAssignedCards = (agentId: string) => { return []; /* TODO: Implement */ };
  const getAgentsAssignedToCard = (cardId: string) => { return []; /* TODO: Implement */ };


  return (
    <AgentBoardControllerContext.Provider
      value={{
        agents,
        isAgentSystemActive,
        startAgent,
        stopAgent,
        agentLogs,
        clearLogs,
        agentStatus,
        registerAgent,
        removeAgent,
        assignTaskToAgent,
        unassignTaskFromAgent,
        updateAgentStatus,
        updateAgentResourceUsage,
        getAgentById,
        getAgentsByType,
        getAgentsByStatus,
        getAgentAssignedCards,
        getAgentsAssignedToCard,
      }}
    >
      {children}
    </AgentBoardControllerContext.Provider>
  )
}

export function useAgentBoardController() {
  const context = useContext(AgentBoardControllerContext)
  if (context === undefined) {
    throw new Error("useAgentBoardController must be used within an AgentBoardControllerProvider")
  }
  return context
}