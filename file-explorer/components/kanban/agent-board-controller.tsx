"use client"

import type React from "react"
import { createContext, useContext, useState } from "react"
import { useBoard } from "./board-context"
import { useToast } from "@/hooks/use-toast"

type AgentBoardControllerContextType = {
  isAgentRunning: boolean
  startAgent: () => void
  stopAgent: () => void
  agentLogs: string[]
  clearLogs: () => void
  agentStatus: "idle" | "running" | "paused" | "error"
}

const AgentBoardControllerContext = createContext<AgentBoardControllerContextType | undefined>(undefined)

export function AgentBoardControllerProvider({ children }: { children: React.ReactNode }) {
  const [isAgentRunning, setIsAgentRunning] = useState(false)
  const [agentLogs, setAgentLogs] = useState<string[]>([])
  const [agentStatus, setAgentStatus] = useState<"idle" | "running" | "paused" | "error">("idle")
  const { activeBoard, updateAgents } = useBoard()
  const { toast } = useToast()

  const startAgent = () => {
    if (!activeBoard) {
      toast({
        title: "Error",
        description: "No active board found",
        variant: "destructive",
      })
      return
    }

    setIsAgentRunning(true)
    setAgentStatus("running")

    // Add a log entry
    const timestamp = new Date().toISOString()
    setAgentLogs((prev) => [...prev, `[${timestamp}] Agent started`])

    toast({
      title: "Agent started",
      description: "The AI agent is now running",
    })
  }

  const stopAgent = () => {
    setIsAgentRunning(false)
    setAgentStatus("idle")

    // Add a log entry
    const timestamp = new Date().toISOString()
    setAgentLogs((prev) => [...prev, `[${timestamp}] Agent stopped`])

    toast({
      title: "Agent stopped",
      description: "The AI agent has been stopped",
    })
  }

  const clearLogs = () => {
    setAgentLogs([])
  }

  return (
    <AgentBoardControllerContext.Provider
      value={{
        isAgentRunning,
        startAgent,
        stopAgent,
        agentLogs,
        clearLogs,
        agentStatus,
      }}
    >
      {children}
    </AgentBoardControllerContext.Provider>
  )
}

export function useAgentBoardController() {
  const context = useContext(AgentBoardControllerContext)
  if (context === undefined) {
    throw new Error("useAgentBoardController must be used within an AgentBoardControllerProvider")
  }
  return context
}
