"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Plus } from "lucide-react"
import { useBoard } from "@/components/kanban/board-context"

export function CreateBoardDialog() {
  const [open, setOpen] = useState(false)
  const [boardName, setBoardName] = useState("")
  const { addBoard } = useBoard()

  const handleCreateBoard = () => {
    if (boardName.trim()) {
      addBoard(boardName)
      setBoardName("")
      setOpen(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant="ghost" size="icon" title="Create new board">
          <Plus className="h-4 w-4" />
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Create New Board</DialogTitle>
          <DialogDescription>
            Give your new Kanban board a name.
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="name" className="text-right">
              Name
            </Label>
            <Input
              id="name"
              value={boardName}
              onChange={(e) => setBoardName(e.target.value)}
              className="col-span-3"
              placeholder="e.g. Project Alpha Kanban"
            />
          </div>
        </div>
        <DialogFooter>
          <Button type="submit" onClick={handleCreateBoard} disabled={!boardName.trim()}>
            Create Board
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}