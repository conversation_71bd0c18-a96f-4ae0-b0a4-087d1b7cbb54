"use client"

import { useRef, useEffect, useState } from "react"
import Editor from "@monaco-editor/react"
import { useTheme } from "next-themes"
import { FileSystemItem } from "./file-sidebar"
import { Button } from "@/components/ui/button"
import { Save, Undo, Redo, Search, Replace } from "lucide-react"
import { useToast } from "@/hooks/use-toast"
import { Textarea } from "@/components/ui/textarea"

// Remove CDN configuration - use local Monaco bundle instead
// Monaco Editor will use the bundled version from @monaco-editor/react

interface MonacoEditorProps {
  file: FileSystemItem | null
  onContentChange?: (content: string) => void
  readOnly?: boolean
}

// Simple fallback text editor component
const FallbackEditor = ({
  content,
  onChange,
  readOnly = false
}: {
  content: string
  onChange: (value: string) => void
  readOnly?: boolean
}) => {
  return (
    <Textarea
      value={content}
      onChange={(e) => onChange(e.target.value)}
      readOnly={readOnly}
      className="w-full h-full resize-none font-mono text-sm border-0 focus-visible:ring-0"
      placeholder="Type your code here..."
    />
  )
}

// Map file extensions to Monaco languages
const getMonacoLanguage = (file: FileSystemItem | null): string => {
  if (!file) return "plaintext";

  console.log("Getting language for file:", file.name, "type:", file.type);

  // Extract extension from file name or type
  let extension: string | undefined;

  if (file.name && file.name.includes('.')) {
    extension = file.name.split('.').pop()?.toLowerCase();
    console.log("Extracted extension from name:", extension);
  } else if (typeof file.type === 'string' && file.type !== 'folder' && file.type !== 'file') {
    extension = file.type.toLowerCase();
    console.log("Using type as extension:", extension);
  }

  if (!extension) {
    console.log("No extension found, using plaintext");
    return "plaintext";
  }

  // Map of file extensions to Monaco language identifiers
  const languageMap: Record<string, string> = {
    js: "javascript",
    jsx: "javascript",
    ts: "typescript",
    tsx: "typescript",
    html: "html",
    htm: "html",
    css: "css",
    scss: "scss",
    sass: "scss",
    less: "less",
    json: "json",
    xml: "xml",
    yaml: "yaml",
    yml: "yaml",
    md: "markdown",
    py: "python",
    java: "java",
    c: "c",
    cpp: "cpp",
    cs: "csharp",
    php: "php",
    rb: "ruby",
    go: "go",
    rs: "rust",
    swift: "swift",
    kt: "kotlin",
    dart: "dart",
    sql: "sql",
    sh: "shell",
    bash: "shell",
    ps1: "powershell",
    dockerfile: "dockerfile",
    vue: "vue",
    svelte: "svelte",
    graphql: "graphql",
    r: "r",
    scala: "scala",
    clj: "clojure",
    fs: "fsharp",
    vb: "vb",
    lua: "lua",
    perl: "perl",
    ini: "ini",
    toml: "toml",
    txt: "plaintext",
  }

  const language = languageMap[extension] || "plaintext";
  console.log("Selected language:", language);
  return language;
}

export default function MonacoEditor({ file, onContentChange, readOnly = false }: MonacoEditorProps) {
  const editorRef = useRef<any>(null)
  const { theme } = useTheme()
  const { toast } = useToast()
  const [content, setContent] = useState("")
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [editorError, setEditorError] = useState<string | null>(null)
  const [useMonaco, setUseMonaco] = useState(true)

  // Update content when file changes
  useEffect(() => {
    console.log("File changed:", file);

    // Reset editor state when file changes
    setEditorError(null);

    if (!file) {
      console.log("No file selected");
      return;
    }

    if (file.content !== undefined) {
      console.log("File has content, length:", file.content.length);
      setContent(file.content);
      setHasUnsavedChanges(false);
    } else if (file.path && window.electronAPI) {
      // If file has path but no content, try to load it
      console.log("File has path but no content, loading from disk:", file.path);
      setIsLoading(true);

      window.electronAPI.readFile(file.path)
        .then(result => {
          if (result.success) {
            console.log("Successfully loaded file content from disk");
            setContent(result.content);
            // Update the file object with content
            file.content = result.content;
          } else {
            console.error("Failed to load file content:", result.error);
            setContent("");
            toast({
              title: "Error loading file",
              description: result.error || "Failed to load file content",
              variant: "destructive",
            });
          }
        })
        .catch(error => {
          console.error("Error reading file:", error);
          setContent("");
          toast({
            title: "Error loading file",
            description: "An unexpected error occurred while loading the file",
            variant: "destructive",
          });
        })
        .finally(() => {
          setIsLoading(false);
        });
    } else {
      // If file has no content and no path, set empty string
      console.log("File has no content and no path, setting empty string");
      setContent("");
      setHasUnsavedChanges(false);
    }
  }, [file, toast])

  const handleEditorWillMount = (monaco: any) => {
    console.log("Monaco beforeMount called")

    // Configure Monaco editor themes
    monaco.editor.defineTheme('custom-dark', {
      base: 'vs-dark',
      inherit: true,
      rules: [],
      colors: {
        'editor.background': '#0f0f0f',
        'editor.foreground': '#d4d4d4',
        'editorLineNumber.foreground': '#858585',
        'editorLineNumber.activeForeground': '#c6c6c6',
        'editor.selectionBackground': '#264f78',
        'editor.inactiveSelectionBackground': '#3a3d41',
      }
    })

    monaco.editor.defineTheme('custom-light', {
      base: 'vs',
      inherit: true,
      rules: [],
      colors: {
        'editor.background': '#ffffff',
        'editor.foreground': '#000000',
        'editorLineNumber.foreground': '#237893',
        'editorLineNumber.activeForeground': '#0b216f',
        'editor.selectionBackground': '#add6ff',
        'editor.inactiveSelectionBackground': '#e5ebf1',
      }
    })
  }

  const handleEditorDidMount = (editor: any, monaco: any) => {
    console.log("Monaco editor mounted successfully")
    editorRef.current = editor
    setEditorError(null)

    // Set theme based on current theme
    const currentTheme = theme === 'dark' ? 'custom-dark' : 'custom-light'
    monaco.editor.setTheme(currentTheme)

    // Configure editor options
    editor.updateOptions({
      fontSize: 14,
      fontFamily: 'JetBrains Mono, Fira Code, Monaco, Consolas, monospace',
      lineNumbers: 'on',
      minimap: { enabled: true },
      scrollBeyondLastLine: false,
      automaticLayout: true,
      tabSize: 2,
      insertSpaces: true,
      wordWrap: 'on',
      bracketPairColorization: { enabled: true },
      guides: {
        bracketPairs: true,
        indentation: true,
      },
      suggest: {
        showKeywords: true,
        showSnippets: true,
      },
      quickSuggestions: {
        other: true,
        comments: true,
        strings: true,
      },
    })

    // Add keyboard shortcuts
    editor.addCommand(monaco.KeyMod.CtrlCmd | monaco.KeyCode.KeyS, () => {
      handleSave()
    })

    editor.addCommand(monaco.KeyMod.CtrlCmd | monaco.KeyCode.KeyF, () => {
      editor.getAction('actions.find').run()
    })

    editor.addCommand(monaco.KeyMod.CtrlCmd | monaco.KeyCode.KeyH, () => {
      editor.getAction('editor.action.startFindReplaceAction').run()
    })
  }

  const handleMonacoError = (error: any) => {
    console.error("Monaco Editor initialization failed:", error)
    setEditorError("Monaco Editor failed to load. Using fallback text editor.")
    setUseMonaco(false)

    toast({
      title: "Editor Notice",
      description: "Advanced editor features unavailable. Using basic text editor.",
      variant: "default",
    })
  }

  const handleContentChange = (value: string | undefined) => {
    const newContent = value || ""
    setContent(newContent)
    setHasUnsavedChanges(newContent !== (file?.content || ""))
    onContentChange?.(newContent)
  }

  const handleSave = async () => {
    if (!file?.path || !hasUnsavedChanges) return

    setIsLoading(true)
    try {
      if (window.electronAPI) {
        const result = await window.electronAPI.saveFile(file.path, content)
        if (result.success) {
          setHasUnsavedChanges(false)
          toast({
            title: "File saved",
            description: `${file.name} has been saved successfully.`,
          })
        } else {
          toast({
            title: "Save failed",
            description: result.error || "Failed to save file",
            variant: "destructive",
          })
        }
      }
    } catch (error) {
      console.error('Error saving file:', error)
      toast({
        title: "Save failed",
        description: "An unexpected error occurred while saving the file",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleUndo = () => {
    editorRef.current?.trigger('keyboard', 'undo', null)
  }

  const handleRedo = () => {
    editorRef.current?.trigger('keyboard', 'redo', null)
  }

  const handleFind = () => {
    editorRef.current?.getAction('actions.find').run()
  }

  const handleReplace = () => {
    editorRef.current?.getAction('editor.action.startFindReplaceAction').run()
  }

  // Update theme when it changes
  useEffect(() => {
    if (editorRef.current) {
      const monaco = (window as any).monaco
      if (monaco) {
        const currentTheme = theme === 'dark' ? 'custom-dark' : 'custom-light'
        monaco.editor.setTheme(currentTheme)
      }
    }
  }, [theme])

  if (!file) {
    return (
      <div className="h-full flex items-center justify-center text-muted-foreground">
        <div className="text-center">
          <p>No file selected</p>
          <p className="text-sm mt-2">Select a file from the sidebar to start editing</p>
        </div>
      </div>
    )
  }

  return (
    <div className="h-full flex flex-col">
      {/* Editor toolbar */}
      <div className="flex items-center justify-between p-2 border-b border-border bg-background">
        <div className="flex items-center space-x-2">
          <span className="text-sm font-medium">{file.name}</span>
          {hasUnsavedChanges && (
            <span className="text-xs text-muted-foreground">• Modified</span>
          )}
        </div>
        <div className="flex items-center space-x-1">
          <Button
            variant="ghost"
            size="sm"
            onClick={handleUndo}
            title="Undo (Ctrl+Z)"
          >
            <Undo className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={handleRedo}
            title="Redo (Ctrl+Y)"
          >
            <Redo className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={handleFind}
            title="Find (Ctrl+F)"
          >
            <Search className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={handleReplace}
            title="Replace (Ctrl+H)"
          >
            <Replace className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={handleSave}
            disabled={!hasUnsavedChanges || isLoading || readOnly}
            title="Save (Ctrl+S)"
          >
            <Save className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Editor */}
      <div className="flex-1">
        {isLoading ? (
          <div className="flex items-center justify-center h-full">
            <div className="flex flex-col items-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mb-4"></div>
              <p className="text-sm text-muted-foreground">Loading file content...</p>
            </div>
          </div>
        ) : editorError && !useMonaco ? (
          <div className="h-full">
            <FallbackEditor
              content={content}
              onChange={handleContentChange}
              readOnly={readOnly}
            />
          </div>
        ) : (
          <Editor
            height="100%"
            language={getMonacoLanguage(file)}
            value={content}
            onChange={handleContentChange}
            beforeMount={handleEditorWillMount}
            onMount={handleEditorDidMount}
            theme={theme === 'dark' ? 'custom-dark' : 'custom-light'}
            options={{
              readOnly: readOnly || isLoading,
              automaticLayout: true,
              minimap: { enabled: true },
              scrollBeyondLastLine: false,
              fontSize: 14,
              fontFamily: 'JetBrains Mono, Fira Code, Monaco, Consolas, monospace',
            }}
            loading={
              <div className="flex items-center justify-center h-full">
                <div className="flex flex-col items-center">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mb-4"></div>
                  <p className="text-sm text-muted-foreground">Initializing editor...</p>
                </div>
              </div>
            }
            onError={handleMonacoError}
          />
        )}
      </div>
    </div>
  )
}