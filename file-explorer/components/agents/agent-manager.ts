// components/agents/agent-manager.ts
import { <PERSON><PERSON><PERSON>, Agent<PERSON><PERSON><PERSON>g, AgentContext, AgentResponse, AgentMessage } from './agent-base';
import { MicromanagerAgent } from './micromanager-agent';
import { InternAgent } from './implementation/intern-agent';
import { JuniorAgent } from './implementation/junior-agent';
import { MidLevelAgent } from './implementation/midlevel-agent';
import { SeniorAgent } from './implementation/senior-agent';
import { ResearcherAgent } from './specialized/researcher-agent';
import { ArchitectAgent } from './specialized/architect-agent';
import { DesignerAgent } from './specialized/designer-agent';
import { TesterAgent } from './specialized/tester-agent';

export interface AgentStatus {
  id: string;
  name: string;
  type: string;
  status: 'idle' | 'busy' | 'error' | 'offline';
  currentTask?: string;
  lastActivity: number;
  tokensUsed: number;
  tasksCompleted: number;
  errorCount: number;
  healthScore: number; // 0-100
}

export interface TaskAssignment {
  taskId: string;
  agentId: string;
  context: AgentContext;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  deadline?: number;
  retryCount: number;
  maxRetries: number;
  status: 'pending' | 'assigned' | 'in_progress' | 'completed' | 'failed';
  createdAt: number;
  updatedAt: number;
}

export class AgentManager {
  private agents: Map<string, AgentBase> = new Map();
  private agentStatuses: Map<string, AgentStatus> = new Map();
  private taskQueue: TaskAssignment[] = [];
  private activeTasks: Map<string, TaskAssignment> = new Map();
  private taskHistory: TaskAssignment[] = [];
  private messageListeners: ((message: AgentMessage) => void)[] = [];

  constructor() {
    this.initializeAgents();
  }

  private initializeAgents(): void {
    // Initialize all agent types
    const agentConfigs: AgentConfig[] = [
      { id: 'micromanager', name: 'Micromanager', type: 'orchestrator' },
      { id: 'intern', name: 'Intern', type: 'implementation' },
      { id: 'junior', name: 'Junior', type: 'implementation' },
      { id: 'midlevel', name: 'MidLevel', type: 'implementation' },
      { id: 'senior', name: 'Senior', type: 'implementation' },
      { id: 'researcher', name: 'Researcher', type: 'specialized' },
      { id: 'architect', name: 'Architect', type: 'specialized' },
      { id: 'designer', name: 'Designer', type: 'specialized' }
    ];

    agentConfigs.forEach(config => {
      const agent = this.createAgent(config);
      if (agent) {
        this.agents.set(config.id, agent);
        this.agentStatuses.set(config.id, {
          id: config.id,
          name: config.name,
          type: config.type,
          status: 'idle',
          lastActivity: Date.now(),
          tokensUsed: 0,
          tasksCompleted: 0,
          errorCount: 0,
          healthScore: 100
        });
      }
    });
  }

  private createAgent(config: AgentConfig): AgentBase | null {
    switch (config.id) {
      case 'micromanager':
        return new MicromanagerAgent(config);
      case 'intern':
        return new InternAgent(config);
      case 'junior':
        return new JuniorAgent(config);
      case 'midlevel':
        return new MidLevelAgent(config);
      case 'senior':
        return new SeniorAgent(config);
      case 'researcher':
        return new ResearcherAgent(config);
      case 'architect':
        return new ArchitectAgent(config);
      case 'designer':
        return new DesignerAgent(config);
      case 'tester':
        return new TesterAgent(config);
      default:
        console.warn(`Unknown agent type: ${config.id}`);
        return null;
    }
  }

  // Public API methods
  public getAgents(): AgentBase[] {
    return Array.from(this.agents.values());
  }

  public getAgent(id: string): AgentBase | null {
    return this.agents.get(id) || null;
  }

  public getAgentStatus(id: string): AgentStatus | null {
    return this.agentStatuses.get(id) || null;
  }

  public getAllAgentStatuses(): AgentStatus[] {
    return Array.from(this.agentStatuses.values());
  }

  public async assignTask(
    agentId: string,
    context: AgentContext,
    priority: TaskAssignment['priority'] = 'medium',
    maxRetries = 3
  ): Promise<string> {
    const taskId = `task_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    const assignment: TaskAssignment = {
      taskId,
      agentId,
      context,
      priority,
      retryCount: 0,
      maxRetries,
      status: 'pending',
      createdAt: Date.now(),
      updatedAt: Date.now()
    };

    this.taskQueue.push(assignment);
    this.processTaskQueue();

    return taskId;
  }

  public async executeTask(agentId: string, context: AgentContext): Promise<AgentResponse> {
    const agent = this.agents.get(agentId);
    if (!agent) {
      throw new Error(`Agent with id '${agentId}' not found`);
    }

    const status = this.agentStatuses.get(agentId);
    if (!status) {
      throw new Error(`Agent status for '${agentId}' not found`);
    }

    // Update agent status
    status.status = 'busy';
    status.currentTask = context.task;
    status.lastActivity = Date.now();

    try {
      const startTime = Date.now();
      const response = await agent.execute(context);
      const executionTime = Date.now() - startTime;

      // Update status based on response
      status.status = 'idle';
      status.currentTask = undefined;
      status.lastActivity = Date.now();
      status.tokensUsed += response.tokensUsed || 0;

      if (response.success) {
        status.tasksCompleted++;
        status.healthScore = Math.min(100, status.healthScore + 1);
      } else {
        status.errorCount++;
        status.healthScore = Math.max(0, status.healthScore - 5);
      }

      return {
        ...response,
        executionTime
      };
    } catch (error) {
      status.status = 'error';
      status.currentTask = undefined;
      status.errorCount++;
      status.healthScore = Math.max(0, status.healthScore - 10);

      throw error;
    }
  }

  private async processTaskQueue(): Promise<void> {
    // Sort queue by priority
    this.taskQueue.sort((a, b) => {
      const priorityOrder = { urgent: 0, high: 1, medium: 2, low: 3 };
      return priorityOrder[a.priority] - priorityOrder[b.priority];
    });

    for (let i = this.taskQueue.length - 1; i >= 0; i--) {
      const task = this.taskQueue[i];
      const agentStatus = this.agentStatuses.get(task.agentId);

      if (agentStatus && agentStatus.status === 'idle') {
        // Remove from queue and add to active tasks
        this.taskQueue.splice(i, 1);
        this.activeTasks.set(task.taskId, task);

        // Execute task asynchronously
        this.executeTaskWithRetry(task);
      }
    }
  }

  private async executeTaskWithRetry(task: TaskAssignment): Promise<void> {
    task.status = 'in_progress';
    task.updatedAt = Date.now();

    try {
      const response = await this.executeTask(task.agentId, task.context);

      if (response.success) {
        task.status = 'completed';
        this.notifyTaskCompletion(task, response);
      } else {
        throw new Error(response.error || 'Task execution failed');
      }
    } catch (error) {
      task.retryCount++;

      if (task.retryCount < task.maxRetries) {
        task.status = 'pending';
        this.taskQueue.push(task); // Add back to queue for retry
      } else {
        task.status = 'failed';
        this.notifyTaskFailure(task, error);
      }
    } finally {
      task.updatedAt = Date.now();

      if (task.status === 'completed' || task.status === 'failed') {
        this.activeTasks.delete(task.taskId);
        this.taskHistory.push(task);
      }
    }
  }

  private notifyTaskCompletion(task: TaskAssignment, response: AgentResponse): void {
    const message: AgentMessage = {
      agentId: task.agentId,
      taskId: task.taskId,
      type: 'completion',
      message: `Task completed successfully: ${task.context.task}`,
      timestamp: Date.now()
    };

    this.broadcastMessage(message);
  }

  private notifyTaskFailure(task: TaskAssignment, error: any): void {
    const message: AgentMessage = {
      agentId: task.agentId,
      taskId: task.taskId,
      type: 'error',
      message: `Task failed after ${task.retryCount} retries: ${error.message || error}`,
      severity: 'high',
      timestamp: Date.now()
    };

    this.broadcastMessage(message);
  }

  public onMessage(listener: (message: AgentMessage) => void): void {
    this.messageListeners.push(listener);
  }

  public offMessage(listener: (message: AgentMessage) => void): void {
    const index = this.messageListeners.indexOf(listener);
    if (index > -1) {
      this.messageListeners.splice(index, 1);
    }
  }

  private broadcastMessage(message: AgentMessage): void {
    this.messageListeners.forEach(listener => listener(message));
  }

  public getTaskHistory(): TaskAssignment[] {
    return [...this.taskHistory];
  }

  public getActiveTasks(): TaskAssignment[] {
    return Array.from(this.activeTasks.values());
  }

  public getQueuedTasks(): TaskAssignment[] {
    return [...this.taskQueue];
  }

  public getTaskById(taskId: string): TaskAssignment | null {
    return this.activeTasks.get(taskId) ||
           this.taskQueue.find(t => t.taskId === taskId) ||
           this.taskHistory.find(t => t.taskId === taskId) ||
           null;
  }

  public cancelTask(taskId: string): boolean {
    // Remove from queue if pending
    const queueIndex = this.taskQueue.findIndex(t => t.taskId === taskId);
    if (queueIndex > -1) {
      this.taskQueue.splice(queueIndex, 1);
      return true;
    }

    // Cancel active task (note: actual cancellation would need agent cooperation)
    if (this.activeTasks.has(taskId)) {
      const task = this.activeTasks.get(taskId)!;
      task.status = 'failed';
      task.updatedAt = Date.now();
      this.activeTasks.delete(taskId);
      this.taskHistory.push(task);
      return true;
    }

    return false;
  }
}