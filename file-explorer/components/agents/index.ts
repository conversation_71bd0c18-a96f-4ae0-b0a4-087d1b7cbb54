// components/agents/index.ts
// Central export file for all agent-related components

// Base classes and interfaces
export { AgentBase } from './agent-base';
export type {
  AgentConfig,
  AgentContext,
  AgentResponse,
  AgentMessage
} from './agent-base';

// Import types for internal use
import { AgentBase, AgentConfig, AgentContext, AgentResponse } from './agent-base';

// Agent Manager
export { AgentManager } from './agent-manager';
export type {
  AgentStatus,
  TaskAssignment
} from './agent-manager';

// Core Orchestrator
export { MicromanagerAgent } from './micromanager-agent';
export type {
  TaskDecomposition,
  SubTask,
  TaskDependency
} from './micromanager-agent';

// Implementation Agents
export { InternAgent } from './implementation/intern-agent';
export { JuniorAgent } from './implementation/junior-agent';
export { MidLevelAgent } from './implementation/midlevel-agent';
export { SeniorAgent } from './implementation/senior-agent';
export type { FileModification } from './implementation/midlevel-agent';

// Specialized Agents
export { ResearcherAgent } from './specialized/researcher-agent';
export { ArchitectAgent } from './specialized/architect-agent';
export { DesignerAgent } from './specialized/designer-agent';
export { TesterAgent } from './specialized/tester-agent';
export type {
  ResearchFindings,
  CodePattern,
  DependencyMap,
  NamingConvention,
  ApiContract,
  BusinessRule,
  ArchitecturalInsight,
  CodeExample
} from './specialized/researcher-agent';

// UI Integration Component
export { AgentIntegration } from './agent-integration';

// Agent Factory for dynamic creation
export class AgentFactory {
  static createAgent(config: AgentConfig): AgentBase | null {
    const { MicromanagerAgent } = require('./micromanager-agent');
    const { InternAgent } = require('./implementation/intern-agent');
    const { JuniorAgent } = require('./implementation/junior-agent');
    const { MidLevelAgent } = require('./implementation/midlevel-agent');
    const { SeniorAgent } = require('./implementation/senior-agent');
    const { ResearcherAgent } = require('./specialized/researcher-agent');
    const { ArchitectAgent } = require('./specialized/architect-agent');
    const { DesignerAgent } = require('./specialized/designer-agent');
    const { TesterAgent } = require('./specialized/tester-agent');

    switch (config.id) {
      case 'micromanager':
        return new MicromanagerAgent(config);
      case 'intern':
        return new InternAgent(config);
      case 'junior':
        return new JuniorAgent(config);
      case 'midlevel':
        return new MidLevelAgent(config);
      case 'senior':
        return new SeniorAgent(config);
      case 'researcher':
        return new ResearcherAgent(config);
      case 'architect':
        return new ArchitectAgent(config);
      case 'designer':
        return new DesignerAgent(config);
      case 'tester':
        return new TesterAgent(config);
      default:
        console.warn(`Unknown agent type: ${config.id}`);
        return null;
    }
  }

  static getAvailableAgents(): string[] {
    return [
      'micromanager',
      'intern',
      'junior',
      'midlevel',
      'senior',
      'researcher',
      'architect',
      'designer',
      'tester'
    ];
  }

  static getAgentCapabilities(agentId: string): string[] {
    const agent = this.createAgent({ id: agentId, name: agentId, type: 'temp' });
    return agent ? agent.getCapabilities() : [];
  }
}

// Utility functions for agent system
export const AgentUtils = {
  /**
   * Estimate task complexity based on description
   */
  estimateTaskComplexity(task: string): 'simple' | 'moderate' | 'complex' | 'very_complex' {
    const complexityIndicators = {
      simple: ['simple', 'basic', 'straightforward', 'quick'],
      complex: ['complex', 'advanced', 'sophisticated', 'intricate', 'multiple', 'integration'],
      very_complex: ['architecture', 'system design', 'distributed', 'performance critical']
    };

    const taskLower = task.toLowerCase();

    if (complexityIndicators.very_complex.some(indicator => taskLower.includes(indicator))) {
      return 'very_complex';
    }
    if (complexityIndicators.complex.some(indicator => taskLower.includes(indicator))) {
      return 'complex';
    }
    if (complexityIndicators.simple.some(indicator => taskLower.includes(indicator))) {
      return 'simple';
    }

    return 'moderate';
  },

  /**
   * Recommend best agent for a task
   */
  recommendAgent(task: string, _availableAgents: string[] = []): string {
    const taskLower = task.toLowerCase();
    const complexity = this.estimateTaskComplexity(task);

    // Always use micromanager for complex orchestration
    if (complexity === 'very_complex' || taskLower.includes('plan') || taskLower.includes('orchestrate')) {
      return 'micromanager';
    }

    // Research tasks
    if (taskLower.includes('research') || taskLower.includes('analyze') || taskLower.includes('investigate')) {
      return 'researcher';
    }

    // Architecture tasks
    if (taskLower.includes('architecture') || taskLower.includes('design system') || taskLower.includes('structure')) {
      return 'architect';
    }

    // UI/Design tasks
    if (taskLower.includes('ui') || taskLower.includes('design') || taskLower.includes('style') || taskLower.includes('component')) {
      return 'designer';
    }

    // Implementation tasks based on complexity
    switch (complexity) {
      case 'simple':
        return 'intern';
      case 'moderate':
        return 'junior';
      case 'complex':
        return 'midlevel';
      default:
        return 'senior';
    }
  },

  /**
   * Format agent response for display
   */
  formatAgentResponse(response: AgentResponse): string {
    if (!response.success) {
      return `❌ Error: ${response.error || 'Unknown error'}`;
    }

    let formatted = `✅ Success\n`;
    if (response.content) {
      formatted += `\nResult:\n${response.content}`;
    }
    if (response.suggestions && response.suggestions.length > 0) {
      formatted += `\n\nSuggestions:\n${response.suggestions.map((s: string) => `• ${s}`).join('\n')}`;
    }
    if (response.tokensUsed) {
      formatted += `\n\nTokens used: ${response.tokensUsed.toLocaleString()}`;
    }
    if (response.executionTime) {
      formatted += `\nExecution time: ${(response.executionTime / 1000).toFixed(2)}s`;
    }

    return formatted;
  },

  /**
   * Validate agent context
   */
  validateContext(context: AgentContext): { valid: boolean; error?: string } {
    if (!context.task || context.task.trim().length === 0) {
      return { valid: false, error: 'Task description is required' };
    }

    if (context.task.length > 10000) {
      return { valid: false, error: 'Task description is too long (max 10,000 characters)' };
    }

    return { valid: true };
  },

  /**
   * Create agent context from simple task description
   */
  createContext(task: string, files?: string[], metadata?: Record<string, any>): AgentContext {
    return {
      task: task.trim(),
      files,
      metadata: {
        createdAt: Date.now(),
        source: 'utils',
        ...metadata
      }
    };
  }
};

// Constants for agent system configuration
export const AGENT_CONSTANTS = {
  MAX_TASK_LENGTH: 10000,
  MAX_RETRY_ATTEMPTS: 3,
  DEFAULT_TIMEOUT: 30000, // 30 seconds
  MAX_CONCURRENT_TASKS: 10,
  HEALTH_CHECK_INTERVAL: 5000, // 5 seconds
  MESSAGE_HISTORY_LIMIT: 100,

  AGENT_TYPES: {
    ORCHESTRATOR: 'orchestrator',
    IMPLEMENTATION: 'implementation',
    SPECIALIZED: 'specialized'
  } as const,

  TASK_PRIORITIES: {
    LOW: 'low',
    MEDIUM: 'medium',
    HIGH: 'high',
    URGENT: 'urgent'
  } as const,

  AGENT_STATUS: {
    IDLE: 'idle',
    BUSY: 'busy',
    ERROR: 'error',
    OFFLINE: 'offline'
  } as const
};