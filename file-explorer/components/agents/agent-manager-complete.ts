// components/agents/agent-manager-complete.ts
import { <PERSON>Base, AgentConfig, AgentContext, AgentResponse, AgentMessage } from './agent-base';

// Import all agent types
import { MicromanagerAgent } from './micromanager-agent';
import { InternAgent } from './implementation/intern-agent';
import { JuniorAgent } from './implementation/junior-agent';
import { MidLevelAgent } from './implementation/midlevel-agent';
import { SeniorAgent } from './implementation/senior-agent';
import { ResearcherAgent } from './specialized/researcher-agent';
import { ArchitectAgent } from './specialized/architect-agent';
import { DesignerAgent } from './specialized/designer-agent';
import { TesterAgent } from './specialized/tester-agent';

// Import middleware components
import { AgentStateMonitorAgent } from '../middleware/agent-state-monitor';
import { ErrorResolutionCoordinatorAgent } from '../middleware/error-resolution-coordinator';
import { ContinuousLearningAgent } from '../middleware/continuous-learning-agent';
import { TaskClassifierAgent } from '../middleware/task-classifier';
import { ResourceOptimizerAgent } from '../middleware/resource-optimizer';
import { ContextProvider } from '../middleware/context-provider';
import { ResultValidator } from '../middleware/result-validator';
import { ExecutionManager } from '../middleware/execution-manager';

export interface AgentStatus {
  id: string;
  name: string;
  type: 'orchestrator' | 'implementation' | 'specialized' | 'middleware';
  status: 'idle' | 'busy' | 'error' | 'offline';
  currentTask?: string;
  lastActivity: number;
  tokensUsed: number;
  tasksCompleted: number;
  errorCount: number;
  healthScore: number; // 0-100
  capabilities: string[];
}

export interface TaskAssignment {
  taskId: string;
  agentId: string;
  context: AgentContext;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  deadline?: number;
  retryCount: number;
  maxRetries: number;
  status: 'pending' | 'assigned' | 'in_progress' | 'completed' | 'failed' | 'escalated';
  createdAt: number;
  updatedAt: number;
  escalationReason?: string;
}

export interface SystemMetrics {
  totalTasks: number;
  successfulTasks: number;
  failedTasks: number;
  averageResponseTime: number;
  totalTokensUsed: number;
  systemHealthScore: number;
  activeAgents: number;
  queueLength: number;
}

export class CompleteAgentManager {
  private agents: Map<string, AgentBase> = new Map();
  private agentStatuses: Map<string, AgentStatus> = new Map();
  private taskQueue: TaskAssignment[] = [];
  private activeTasks: Map<string, TaskAssignment> = new Map();
  private taskHistory: TaskAssignment[] = [];
  private messageListeners: ((message: AgentMessage) => void)[] = [];

  // Middleware components
  private stateMonitor: AgentStateMonitorAgent;
  private errorCoordinator: ErrorResolutionCoordinatorAgent;
  private learningAgent: ContinuousLearningAgent;
  private taskClassifier: TaskClassifierAgent;
  private resourceOptimizer: ResourceOptimizerAgent;
  private contextProvider: ContextProvider;
  private resultValidator: ResultValidator;
  private executionManager: ExecutionManager;

  // System metrics
  private systemMetrics: SystemMetrics = {
    totalTasks: 0,
    successfulTasks: 0,
    failedTasks: 0,
    averageResponseTime: 0,
    totalTokensUsed: 0,
    systemHealthScore: 100,
    activeAgents: 0,
    queueLength: 0
  };

  constructor() {
    this.initializeMiddleware();
    this.initializeAgents();
    this.startSystemMonitoring();
  }

  private initializeMiddleware(): void {
    // Initialize middleware components
    this.stateMonitor = new AgentStateMonitorAgent({ 
      id: 'state_monitor', 
      name: 'State Monitor', 
      type: 'middleware' 
    });

    this.errorCoordinator = new ErrorResolutionCoordinatorAgent({ 
      id: 'error_coordinator', 
      name: 'Error Coordinator', 
      type: 'middleware' 
    });

    this.learningAgent = new ContinuousLearningAgent({ 
      id: 'learning_agent', 
      name: 'Learning Agent', 
      type: 'middleware' 
    });

    this.taskClassifier = new TaskClassifierAgent({ 
      id: 'task_classifier', 
      name: 'Task Classifier', 
      type: 'middleware' 
    });

    this.resourceOptimizer = new ResourceOptimizerAgent({ 
      id: 'resource_optimizer', 
      name: 'Resource Optimizer', 
      type: 'middleware' 
    });

    this.contextProvider = new ContextProvider();
    this.resultValidator = new ResultValidator();
    this.executionManager = new ExecutionManager();

    // Set up alert handling
    this.stateMonitor.onAlert((alert) => {
      this.handleHealthAlert(alert);
    });
  }

  private initializeAgents(): void {
    const agentConfigs: AgentConfig[] = [
      // Orchestrator
      { id: 'micromanager', name: '🤖 Micromanager', type: 'orchestrator' },
      
      // Implementation agents
      { id: 'intern', name: '1️⃣ Intern', type: 'implementation' },
      { id: 'junior', name: '2️⃣ Junior', type: 'implementation' },
      { id: 'midlevel', name: '3️⃣ MidLevel', type: 'implementation' },
      { id: 'senior', name: '4️⃣ Senior', type: 'implementation' },
      
      // Specialized agents
      { id: 'researcher', name: '📘 Researcher', type: 'specialized' },
      { id: 'architect', name: '🏗️ Architect', type: 'specialized' },
      { id: 'designer', name: '🎨 Designer', type: 'specialized' },
      { id: 'tester', name: '🧪 Tester', type: 'specialized' }
    ];

    agentConfigs.forEach(config => {
      const agent = this.createAgent(config);
      if (agent) {
        this.agents.set(config.id, agent);
        
        // Register with state monitor
        this.stateMonitor.registerAgent(config.id);
        
        // Create initial status
        this.agentStatuses.set(config.id, {
          id: config.id,
          name: config.name,
          type: config.type as any,
          status: 'idle',
          lastActivity: Date.now(),
          tokensUsed: 0,
          tasksCompleted: 0,
          errorCount: 0,
          healthScore: 100,
          capabilities: agent.getCapabilities()
        });
      }
    });

    this.updateSystemMetrics();
  }

  private createAgent(config: AgentConfig): AgentBase | null {
    switch (config.id) {
      case 'micromanager':
        return new MicromanagerAgent(config);
      case 'intern':
        return new InternAgent(config);
      case 'junior':
        return new JuniorAgent(config);
      case 'midlevel':
        return new MidLevelAgent(config);
      case 'senior':
        return new SeniorAgent(config);
      case 'researcher':
        return new ResearcherAgent(config);
      case 'architect':
        return new ArchitectAgent(config);
      case 'designer':
        return new DesignerAgent(config);
      case 'tester':
        return new TesterAgent(config);
      default:
        console.warn(`Unknown agent type: ${config.id}`);
        return null;
    }
  }

  // Enhanced public API methods
  public async submitTask(
    task: string,
    files?: string[],
    priority: TaskAssignment['priority'] = 'medium',
    metadata?: Record<string, any>
  ): Promise<string> {
    const context: AgentContext = {
      task,
      files,
      metadata: {
        ...metadata,
        submittedAt: Date.now(),
        source: 'user'
      }
    };

    // Step 1: Classify the task
    const classification = await this.taskClassifier.execute({
      task: `Classify task: ${task}`,
      metadata: { originalTask: task }
    });

    let recommendedAgent = 'junior'; // Default
    if (classification.success && classification.metadata?.classification) {
      recommendedAgent = classification.metadata.classification.recommendedAgent;
    }

    // Step 2: Get resource optimization
    const optimization = await this.resourceOptimizer.execute({
      task: `Optimize resources for: ${task}`,
      metadata: { agentId: recommendedAgent, taskType: 'classification' }
    });

    // Step 3: Assign task
    return await this.assignTask(recommendedAgent, context, priority);
  }

  public async assignTask(
    agentId: string,
    context: AgentContext,
    priority: TaskAssignment['priority'] = 'medium',
    maxRetries = 3
  ): Promise<string> {
    const taskId = `task_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    // Gather enhanced context
    const enhancedContext = await this.enhanceContext(context, agentId);

    const assignment: TaskAssignment = {
      taskId,
      agentId,
      context: enhancedContext,
      priority,
      retryCount: 0,
      maxRetries,
      status: 'pending',
      createdAt: Date.now(),
      updatedAt: Date.now()
    };

    this.taskQueue.push(assignment);
    this.systemMetrics.queueLength = this.taskQueue.length;
    
    // Process queue
    await this.processTaskQueue();

    return taskId;
  }

  private async enhanceContext(context: AgentContext, agentId: string): Promise<AgentContext> {
    try {
      // Use context provider to gather relevant context
      const contextPackage = await this.contextProvider.gatherContext(
        context.task,
        context.files || []
      );

      return {
        ...context,
        codeContext: contextPackage.relevantCode.join('\n'),
        rules: contextPackage.ruleReferences,
        dependencies: contextPackage.dependencies,
        metadata: {
          ...context.metadata,
          patterns: contextPackage.patterns,
          contextEnhanced: true
        }
      };
    } catch (error) {
      console.warn('Context enhancement failed:', error);
      return context;
    }
  }

  public async executeTask(agentId: string, context: AgentContext): Promise<AgentResponse> {
    const agent = this.agents.get(agentId);
    if (!agent) {
      throw new Error(`Agent with id '${agentId}' not found`);
    }

    const status = this.agentStatuses.get(agentId);
    if (!status) {
      throw new Error(`Agent status for '${agentId}' not found`);
    }

    // Update agent status
    status.status = 'busy';
    status.currentTask = context.task;
    status.lastActivity = Date.now();

    const startTime = Date.now();

    try {
      // Execute task
      const response = await agent.execute(context);
      const executionTime = Date.now() - startTime;

      // Validate result if successful
      if (response.success && response.content) {
        const validation = await this.resultValidator.validateCode(
          response.content,
          this.extractLanguage(context)
        );
        
        if (!validation.valid) {
          response.suggestions = [
            ...(response.suggestions || []),
            ...validation.suggestions,
            'Validation issues detected - review before implementation'
          ];
        }
      }

      // Update metrics and learning
      this.updateAgentStatus(agentId, response, executionTime);
      this.recordTaskCompletion(agentId, context, response, executionTime);

      return {
        ...response,
        executionTime
      };

    } catch (error) {
      const executionTime = Date.now() - startTime;
      
      status.status = 'error';
      status.currentTask = undefined;
      status.errorCount++;
      status.healthScore = Math.max(0, status.healthScore - 10);

      // Record failure for learning
      this.recordTaskCompletion(agentId, context, {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      }, executionTime);

      throw error;
    }
  }

  private extractLanguage(context: AgentContext): string {
    if (context.files) {
      const extensions = context.files.map(file => file.split('.').pop()).filter(Boolean);
      if (extensions.length > 0) {
        return extensions[0] || 'javascript';
      }
    }
    return 'javascript';
  }

  private updateAgentStatus(agentId: string, response: AgentResponse, executionTime: number): void {
    const status = this.agentStatuses.get(agentId);
    if (!status) return;

    status.status = 'idle';
    status.currentTask = undefined;
    status.lastActivity = Date.now();
    status.tokensUsed += response.tokensUsed || 0;

    if (response.success) {
      status.tasksCompleted++;
      status.healthScore = Math.min(100, status.healthScore + 1);
    } else {
      status.errorCount++;
      status.healthScore = Math.max(0, status.healthScore - 5);
    }

    // Update state monitor
    this.stateMonitor.updateAgentMetrics(agentId, {
      tokensUsed: response.tokensUsed,
      responseTime: executionTime,
      success: response.success,
      taskCompleted: true
    });
  }

  private recordTaskCompletion(
    agentId: string,
    context: AgentContext,
    response: AgentResponse,
    executionTime: number
  ): void {
    // Record in learning system
    this.learningAgent.recordTaskCompletion(
      agentId,
      this.extractTaskType(context.task),
      response.success || false,
      executionTime,
      response.tokensUsed || 0,
      context,
      response.content,
      response.error
    );

    // Update system metrics
    this.systemMetrics.totalTasks++;
    this.systemMetrics.totalTokensUsed += response.tokensUsed || 0;
    
    if (response.success) {
      this.systemMetrics.successfulTasks++;
    } else {
      this.systemMetrics.failedTasks++;
    }

    // Update average response time
    this.systemMetrics.averageResponseTime = 
      (this.systemMetrics.averageResponseTime * (this.systemMetrics.totalTasks - 1) + executionTime) / 
      this.systemMetrics.totalTasks;
  }

  private extractTaskType(task: string): string {
    const taskLower = task.toLowerCase();
    if (taskLower.includes('implement') || taskLower.includes('create')) return 'implementation';
    if (taskLower.includes('design') || taskLower.includes('ui')) return 'design';
    if (taskLower.includes('test')) return 'testing';
    if (taskLower.includes('research') || taskLower.includes('analyze')) return 'research';
    if (taskLower.includes('fix') || taskLower.includes('debug')) return 'debugging';
    return 'general';
  }

  private async processTaskQueue(): Promise<void> {
    // Sort queue by priority and creation time
    this.taskQueue.sort((a, b) => {
      const priorityOrder = { urgent: 0, high: 1, medium: 2, low: 3 };
      const priorityDiff = priorityOrder[a.priority] - priorityOrder[b.priority];
      return priorityDiff !== 0 ? priorityDiff : a.createdAt - b.createdAt;
    });

    const tasksToProcess = [];
    
    for (let i = this.taskQueue.length - 1; i >= 0; i--) {
      const task = this.taskQueue[i];
      const agentStatus = this.agentStatuses.get(task.agentId);

      if (agentStatus && agentStatus.status === 'idle') {
        // Remove from queue and add to processing
        this.taskQueue.splice(i, 1);
        this.activeTasks.set(task.taskId, task);
        tasksToProcess.push(task);
      }
    }

    this.systemMetrics.queueLength = this.taskQueue.length;

    // Execute tasks concurrently
    const processingPromises = tasksToProcess.map(task => this.executeTaskWithRetry(task));
    await Promise.allSettled(processingPromises);
  }

  private async executeTaskWithRetry(task: TaskAssignment): Promise<void> {
    task.status = 'in_progress';
    task.updatedAt = Date.now();

    try {
      const response = await this.executeTask(task.agentId, task.context);

      if (response.success) {
        task.status = 'completed';
        await this.notifyTaskCompletion(task, response);
      } else {
        throw new Error(response.error || 'Task execution failed');
      }

    } catch (error) {
      await this.handleTaskError(task, error);
    } finally {
      task.updatedAt = Date.now();

      if (task.status === 'completed' || task.status === 'failed') {
        this.activeTasks.delete(task.taskId);
        this.taskHistory.push(task);
        
        // Keep only last 1000 completed tasks
        if (this.taskHistory.length > 1000) {
          this.taskHistory.shift();
        }
      }
    }
  }

  private async handleTaskError(task: TaskAssignment, error: any): Promise<void> {
    task.retryCount++;

    // Check if we should escalate to error coordinator
    if (task.retryCount >= 2 && task.retryCount < task.maxRetries) {
      try {
        const resolution = await this.errorCoordinator.resolveError(
          error.message || String(error),
          task.agentId,
          [`Attempt ${task.retryCount}: ${error.message}`],
          task.context
        );

        if (resolution.success) {
          // Error resolved, complete the task
          task.status = 'completed';
          await this.notifyTaskCompletion(task, resolution);
          return;
        }
      } catch (coordinatorError) {
        console.warn('Error coordinator failed:', coordinatorError);
      }
    }

    // Standard retry logic
    if (task.retryCount < task.maxRetries) {
      // Try with a different agent if available
      const alternativeAgent = this.selectAlternativeAgent(task.agentId, task.context);
      if (alternativeAgent) {
        task.agentId = alternativeAgent;
        task.escalationReason = `Escalated from ${task.agentId} after ${task.retryCount} attempts`;
      }
      
      task.status = 'pending';
      this.taskQueue.push(task); // Add back to queue for retry
    } else {
      task.status = 'failed';
      await this.notifyTaskFailure(task, error);
    }
  }

  private selectAlternativeAgent(failedAgentId: string, context: AgentContext): string | null {
    // Escalation hierarchy
    const escalationMap: Record<string, string[]> = {
      'intern': ['junior', 'midlevel'],
      'junior': ['midlevel', 'senior'],
      'midlevel': ['senior'],
      'senior': ['micromanager'], // Last resort: let micromanager decide
    };

    const alternatives = escalationMap[failedAgentId] || [];
    
    // Find first available alternative
    for (const agentId of alternatives) {
      const status = this.agentStatuses.get(agentId);
      if (status && status.status === 'idle' && status.healthScore > 50) {
        return agentId;
      }
    }

    return null;
  }

  private async notifyTaskCompletion(task: TaskAssignment, response: AgentResponse): Promise<void> {
    const message: AgentMessage = {
      agentId: task.agentId,
      taskId: task.taskId,
      type: 'completion',
      message: `Task completed successfully: ${task.context.task}`,
      timestamp: Date.now()
    };

    this.broadcastMessage(message);
  }

  private async notifyTaskFailure(task: TaskAssignment, error: any): Promise<void> {
    const message: AgentMessage = {
      agentId: task.agentId,
      taskId: task.taskId,
      type: 'error',
      message: `Task failed after ${task.retryCount} retries: ${error.message || error}`,
      severity: 'high',
      timestamp: Date.now()
    };

    this.broadcastMessage(message);
  }

  private handleHealthAlert(alert: any): void {
    // Handle health alerts from state monitor
    const message: AgentMessage = {
      agentId: 'state_monitor',
      taskId: 'health_check',
      type: 'error',
      message: `Health Alert - ${alert.agentId}: ${alert.message}`,
      severity: alert.severity === 'red' ? 'high' : 'medium',
      actions: alert.suggestedActions,
      timestamp: Date.now()
    };

    this.broadcastMessage(message);

    // Take automatic actions for critical alerts
    if (alert.severity === 'red') {
      const agentStatus = this.agentStatuses.get(alert.agentId);
      if (agentStatus) {
        agentStatus.status = 'error';
        // Could implement automatic agent restart or model switching here
      }
    }
  }

  private startSystemMonitoring(): void {
    // Update system metrics every 30 seconds
    setInterval(() => {
      this.updateSystemMetrics();
    }, 30000);
  }

  private updateSystemMetrics(): void {
    const allStatuses = Array.from(this.agentStatuses.values());
    
    this.systemMetrics.activeAgents = allStatuses.filter(s => s.status !== 'offline').length;
    this.systemMetrics.systemHealthScore = allStatuses.reduce((sum, s) => sum + s.healthScore, 0) / allStatuses.length;
    this.systemMetrics.queueLength = this.taskQueue.length;
  }

  // Public API methods
  public getSystemMetrics(): SystemMetrics {
    return { ...this.systemMetrics };
  }

  public getAgents(): AgentBase[] {
    return Array.from(this.agents.values());
  }

  public getAgent(id: string): AgentBase | null {
    return this.agents.get(id) || null;
  }

  public getAgentStatus(id: string): AgentStatus | null {
    return this.agentStatuses.get(id) || null;
  }

  public getAllAgentStatuses(): AgentStatus[] {
    return Array.from(this.agentStatuses.values());
  }

  public getActiveTasks(): TaskAssignment[] {
    return Array.from(this.activeTasks.values());
  }

  public getQueuedTasks(): TaskAssignment[] {
    return [...this.taskQueue];
  }

  public getTaskHistory(limit = 50): TaskAssignment[] {
    return this.taskHistory.slice(-limit);
  }

  public getTaskById(taskId: string): TaskAssignment | null {
    return this.activeTasks.get(taskId) ||
           this.taskQueue.find(t => t.taskId === taskId) ||
           this.taskHistory.find(t => t.taskId === taskId) ||
           null;
  }

  public cancelTask(taskId: string): boolean {
    // Remove from queue if pending
    const queueIndex = this.taskQueue.findIndex(t => t.taskId === taskId);
    if (queueIndex > -1) {
      this.taskQueue.splice(queueIndex, 1);
      this.systemMetrics.queueLength = this.taskQueue.length;
      return true;
    }

    // Cancel active task
    if (this.activeTasks.has(taskId)) {
      const task = this.activeTasks.get(taskId)!;
      task.status = 'failed';
      task.updatedAt = Date.now();
      this.activeTasks.delete(taskId);
      this.taskHistory.push(task);
      return true;
    }

    return false;
  }

  // Learning and optimization methods
  public getOptimizationSuggestions(agentId?: string): any[] {
    return this.learningAgent.getOptimizationSuggestions(agentId);
  }

  public getLearningPatterns(category?: string): any[] {
    return this.learningAgent.getLearningPatterns(category);
  }

  public getPerformanceMetrics(agentId?: string): any[] {
    return this.learningAgent.getPerformanceMetrics(agentId);
  }

  public async generateSystemReport(): Promise<string> {
    const metrics = this.getSystemMetrics();
    const agentStatuses = this.getAllAgentStatuses();
    const optimizations = this.getOptimizationSuggestions();

    return `[SYSTEM STATUS REPORT]

OVERVIEW:
- System Health: ${metrics.systemHealthScore.toFixed(1)}%
- Active Agents: ${metrics.activeAgents}/${agentStatuses.length}
- Queue Length: ${metrics.queueLength}
- Total Tasks: ${metrics.totalTasks}
- Success Rate: ${((metrics.successfulTasks / Math.max(metrics.totalTasks, 1)) * 100).toFixed(1)}%

AGENT STATUS:
${agentStatuses.map(agent => {
  const health = agent.healthScore;
  const status = health > 70 ? '🟢' : health > 40 ? '🟡' : '🔴';
  return `${status} ${agent.name}: ${health.toFixed(1)}% (${agent.tasksCompleted} tasks)`;
}).join('\n')}

PERFORMANCE METRICS:
- Average Response Time: ${(metrics.averageResponseTime / 1000).toFixed(2)}s
- Total Tokens Used: ${metrics.totalTokensUsed.toLocaleString()}
- Failed Tasks: ${metrics.failedTasks}

OPTIMIZATION OPPORTUNITIES:
${optimizations.slice(0, 5).map(opt => `- ${opt.description}`).join('\n')}

ACTIVE TASKS: ${this.getActiveTasks().length}
QUEUED TASKS: ${this.getQueuedTasks().length}

[END REPORT]`;
  }

  // Event handling
  public onMessage(listener: (message: AgentMessage) => void): void {
    this.messageListeners.push(listener);
  }

  public offMessage(listener: (message: AgentMessage) => void): void {
    const index = this.messageListeners.indexOf(listener);
    if (index > -1) {
      this.messageListeners.splice(index, 1);
    }
  }

  private broadcastMessage(message: AgentMessage): void {
    this.messageListeners.forEach(listener => listener(message));
  }

  // Cleanup
  public async shutdown(): Promise<void> {
    // Cancel all pending tasks
    this.taskQueue.forEach(task => {
      task.status = 'failed';
      task.updatedAt = Date.now();
    });

    // Clear active tasks
    this.activeTasks.clear();
    
    // Stop monitoring
    if (this.stateMonitor) {
      this.stateMonitor.destroy();
    }

    console.log('Agent Manager shutdown complete');
  }
}