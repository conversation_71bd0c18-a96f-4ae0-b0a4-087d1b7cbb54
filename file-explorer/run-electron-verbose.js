const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

// Log the current directory
console.log('Current directory:', process.cwd());

// Check if test.html exists
const testHtmlPath = path.join(process.cwd(), 'test.html');
console.log('Checking for test.html at:', testHtmlPath);
console.log('File exists:', fs.existsSync(testHtmlPath));

// Check if dist-electron directory exists
const distElectronPath = path.join(process.cwd(), 'dist-electron');
console.log('Checking for dist-electron at:', distElectronPath);
console.log('Directory exists:', fs.existsSync(distElectronPath));

// List files in dist-electron
if (fs.existsSync(distElectronPath)) {
  console.log('Files in dist-electron:');
  fs.readdirSync(distElectronPath).forEach(file => {
    console.log(' -', file);
  });
}

// Check if out directory exists
const outPath = path.join(process.cwd(), 'out');
console.log('Checking for out directory at:', outPath);
console.log('Directory exists:', fs.existsSync(outPath));

// List files in out directory
if (fs.existsSync(outPath)) {
  console.log('Files in out directory:');
  fs.readdirSync(outPath).forEach(file => {
    console.log(' -', file);
  });
}

// Run electron with verbose logging
console.log('Starting Electron...');
const electron = spawn('npx', ['electron', '.'], {
  stdio: 'inherit',
  env: {
    ...process.env,
    ELECTRON_ENABLE_LOGGING: '1',
    ELECTRON_ENABLE_STACK_DUMPING: '1',
    DEBUG: '*'
  }
});

electron.on('close', (code) => {
  console.log(`Electron process exited with code ${code}`);
});
