const { app, BrowserWindow } = require('electron');
const path = require('path');
const fs = require('fs');

// Keep a global reference of the window object, if you don't, the window will
// be closed automatically when the JavaScript object is garbage collected.
let mainWindow;

function createWindow() {
  // Create the browser window.
  mainWindow = new BrowserWindow({
    width: 800,
    height: 600,
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false
    }
  });

  // Log the current directory
  console.log('Current directory:', process.cwd());
  
  // Check if test.html exists
  const testHtmlPath = path.join(process.cwd(), 'test.html');
  console.log('Checking for test.html at:', testHtmlPath);
  console.log('File exists:', fs.existsSync(testHtmlPath));

  // Load the test.html file
  if (fs.existsSync(testHtmlPath)) {
    console.log('Loading test.html from:', testHtmlPath);
    mainWindow.loadFile(testHtmlPath);
  } else {
    console.log('test.html not found, loading fallback');
    mainWindow.loadURL('data:text/html,<html><body><h1>Simple Electron App</h1><p>test.html not found</p></body></html>');
  }

  // Open the DevTools.
  mainWindow.webContents.openDevTools();

  // Emitted when the window is closed.
  mainWindow.on('closed', function() {
    // Dereference the window object, usually you would store windows
    // in an array if your app supports multi windows, this is the time
    // when you should delete the corresponding element.
    mainWindow = null;
  });
}

// This method will be called when Electron has finished
// initialization and is ready to create browser windows.
// Some APIs can only be used after this event occurs.
app.on('ready', createWindow);

// Quit when all windows are closed.
app.on('window-all-closed', function() {
  // On macOS it is common for applications and their menu bar
  // to stay active until the user quits explicitly with Cmd + Q
  if (process.platform !== 'darwin') app.quit();
});

app.on('activate', function() {
  // On macOS it's common to re-create a window in the app when the
  // dock icon is clicked and there are no other windows open.
  if (mainWindow === null) createWindow();
});
