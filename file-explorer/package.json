{"name": "my-v0-project", "version": "0.1.0", "private": true, "main": "dist-electron/main.js", "scripts": {"dev": "next dev", "build": "next build", "start": "npm run build && npm run compile:electron && electron .", "lint": "next lint", "compile:electron": "tsc -p tsconfig.electron.json", "electron:dev": "concurrently \"npm:dev\" \"wait-on http://localhost:3000 && npm run compile:electron && electron . --dev\"", "electron:prod": "npm run compile:electron && npm run build && electron .", "rebuild": "rm -rf out dist-electron && npm run build && npm run compile:electron", "debug": "npm run compile:electron && npm run build && electron . --devtools"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/utilities": "^3.2.2", "@hookform/resolvers": "^3.9.1", "@monaco-editor/react": "^4.7.0", "@radix-ui/react-accordion": "1.2.2", "@radix-ui/react-alert-dialog": "1.1.4", "@radix-ui/react-aspect-ratio": "1.1.1", "@radix-ui/react-avatar": "1.1.2", "@radix-ui/react-checkbox": "1.1.3", "@radix-ui/react-collapsible": "1.1.2", "@radix-ui/react-context-menu": "2.2.4", "@radix-ui/react-dialog": "1.1.4", "@radix-ui/react-dropdown-menu": "2.1.4", "@radix-ui/react-hover-card": "1.1.4", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "2.1.1", "@radix-ui/react-menubar": "1.1.4", "@radix-ui/react-navigation-menu": "1.2.3", "@radix-ui/react-popover": "1.1.4", "@radix-ui/react-progress": "1.1.1", "@radix-ui/react-radio-group": "1.2.2", "@radix-ui/react-scroll-area": "1.2.2", "@radix-ui/react-select": "2.1.4", "@radix-ui/react-separator": "1.1.1", "@radix-ui/react-slider": "1.2.2", "@radix-ui/react-slot": "1.1.1", "@radix-ui/react-switch": "1.1.2", "@radix-ui/react-tabs": "1.1.2", "@radix-ui/react-toast": "1.2.4", "@radix-ui/react-toggle": "1.1.1", "@radix-ui/react-toggle-group": "1.1.1", "@radix-ui/react-tooltip": "1.1.6", "autoprefixer": "^10.4.20", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.0.4", "date-fns": "4.1.0", "embla-carousel-react": "8.5.1", "input-otp": "1.4.1", "lucide-react": "^0.454.0", "monaco-editor": "^0.52.2", "next": "15.2.4", "next-themes": "latest", "react": "^19", "react-day-picker": "8.10.1", "react-dom": "^19", "react-hook-form": "^7.54.1", "react-resizable-panels": "^2.1.7", "recharts": "2.15.0", "sonner": "^1.7.1", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "vaul": "^0.9.6", "zod": "^3.24.1"}, "devDependencies": {"@types/node": "^22", "@types/react": "^19", "@types/react-dom": "^19", "concurrently": "^8.2.2", "cross-env": "^7.0.3", "electron": "^28.3.3", "electron-builder": "^26.0.12", "electron-is-dev": "^2.0.0", "electron-serve": "^2.1.1", "postcss": "^8", "tailwindcss": "^3.4.17", "typescript": "^5", "wait-on": "^7.2.0"}}