import { app, BrowserWindow, shell, ipc<PERSON>ain, dialog } from 'electron';
import path from 'path';
import url from 'url';
import fs from 'fs';
import isDev from 'electron-is-dev';

let mainWindow: BrowserWindow | null;

function createWindow() {
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    webPreferences: {
      nodeIntegration: false, // Best practice for security
      contextIsolation: true, // Best practice for security
      preload: path.join(__dirname, 'preload.js'), // Enable preload script for IPC
      devTools: true, // Always enable DevTools for debugging
      webSecurity: true, // Enable web security
    },
    icon: path.join(__dirname, '../public/placeholder-logo.svg') // Adjust path if needed
  });

  // Set Content Security Policy
  mainWindow.webContents.session.webRequest.onHeadersReceived((details, callback) => {
    callback({
      responseHeaders: {
        ...details.responseHeaders,
        'Content-Security-Policy': [
          "default-src 'self'; script-src 'self' 'unsafe-eval' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data:; font-src 'self' data:; connect-src 'self' ws: wss:;"
        ]
      }
    });
  });

  // Set a default title
  mainWindow.setTitle('CodeFusion - Modern Code Editor');

  // Determine the correct path to load
  const appPath = app.getAppPath();
  console.log('App path:', appPath);

  // Try to load the test HTML file first
  const testHtmlPath = path.join(process.cwd(), 'test.html');
  const testHtmlPathAlt = path.join(__dirname, '../test.html');

  console.log('Looking for test.html at:', testHtmlPath);
  console.log('Alternative path:', testHtmlPathAlt);

  if (fs.existsSync(testHtmlPath)) {
    console.log('Found test.html at:', testHtmlPath);
    mainWindow.loadFile(testHtmlPath);
    mainWindow.webContents.openDevTools();
  } else if (fs.existsSync(testHtmlPathAlt)) {
    console.log('Found test.html at:', testHtmlPathAlt);
    mainWindow.loadFile(testHtmlPathAlt);
    mainWindow.webContents.openDevTools();
  } else if (isDev || process.argv.includes('--dev')) {
    // In development, load from the Next.js dev server
    console.log('Loading from dev server');
    mainWindow.loadURL('http://localhost:3000');
    mainWindow.webContents.openDevTools();
  } else {
    // In production, load the statically exported Next.js app
    try {
      // Find the correct path to the out directory
      const outDir = path.join(__dirname, '../out');
      const outDirAlt = path.join(process.cwd(), 'out');

      console.log('Looking for out directory at:', outDir);
      console.log('Alternative path:', outDirAlt);

      let indexPath;
      if (fs.existsSync(path.join(outDir, 'index.html'))) {
        indexPath = url.format({
          pathname: path.join(outDir, 'index.html'),
          protocol: 'file:',
          slashes: true,
        });
      } else if (fs.existsSync(path.join(outDirAlt, 'index.html'))) {
        indexPath = url.format({
          pathname: path.join(outDirAlt, 'index.html'),
          protocol: 'file:',
          slashes: true,
        });
      } else {
        throw new Error('Could not find index.html in out directory');
      }

      console.log('Loading from:', indexPath);
      mainWindow.loadURL(indexPath);
      mainWindow.webContents.openDevTools(); // Open DevTools in production for debugging
    } catch (error) {
      console.error('Error loading index.html:', error);
      // Show error in window
      mainWindow.loadURL(`data:text/html,<html><body><h1>Error</h1><p>${error}</p></body></html>`);
    }
  }

  // Open external links in the default browser
  mainWindow.webContents.setWindowOpenHandler(({ url }) => {
    shell.openExternal(url);
    return { action: 'deny' };
  });

  mainWindow.on('closed', () => {
    mainWindow = null;
  });
}

app.on('ready', createWindow);

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', () => {
  if (mainWindow === null) {
    createWindow();
  }
});

// IPC handlers for file system operations
ipcMain.handle('select-folder', async () => {
  const result = await dialog.showOpenDialog(mainWindow!, {
    properties: ['openDirectory'],
    title: 'Select Project Folder'
  });

  if (!result.canceled && result.filePaths.length > 0) {
    const folderPath = result.filePaths[0];
    return {
      success: true,
      path: folderPath,
      name: path.basename(folderPath)
    };
  }

  return { success: false };
});

ipcMain.handle('read-directory', async (event, dirPath: string) => {
  try {
    const items = fs.readdirSync(dirPath, { withFileTypes: true });
    const result = [];

    for (const item of items) {
      const itemPath = path.join(dirPath, item.name);
      const stats = fs.statSync(itemPath);

      if (item.isDirectory()) {
        result.push({
          id: Date.now() + Math.random(),
          name: item.name,
          type: 'folder',
          path: itemPath,
          expanded: false,
          files: []
        });
      } else {
        const ext = path.extname(item.name).slice(1);
        result.push({
          id: Date.now() + Math.random(),
          name: item.name,
          type: ext || 'file',
          path: itemPath,
          size: stats.size,
          modified: stats.mtime
        });
      }
    }

    return { success: true, items: result };
  } catch (error) {
    console.error('Error reading directory:', error);
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
  }
});

ipcMain.handle('read-file', async (event, filePath: string) => {
  try {
    const content = fs.readFileSync(filePath, 'utf-8');
    return { success: true, content };
  } catch (error) {
    console.error('Error reading file:', error);
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
  }
});

ipcMain.handle('save-file', async (event, filePath: string, content: string) => {
  try {
    fs.writeFileSync(filePath, content, 'utf-8');
    return { success: true };
  } catch (error) {
    console.error('Error saving file:', error);
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
  }
});

ipcMain.handle('create-file', async (event, filePath: string, content: string = '') => {
  try {
    // Check if file already exists
    if (fs.existsSync(filePath)) {
      return { success: false, error: 'File already exists' };
    }

    // Create directory if it doesn't exist
    const dir = path.dirname(filePath);
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }

    fs.writeFileSync(filePath, content, 'utf-8');
    return { success: true };
  } catch (error) {
    console.error('Error creating file:', error);
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
  }
});

ipcMain.handle('delete-file', async (event, filePath: string) => {
  try {
    const stats = fs.statSync(filePath);
    if (stats.isDirectory()) {
      fs.rmSync(filePath, { recursive: true, force: true });
    } else {
      fs.unlinkSync(filePath);
    }
    return { success: true };
  } catch (error) {
    console.error('Error deleting file:', error);
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
  }
});