import { app, BrowserWindow, shell, ipc<PERSON>ain, dialog } from 'electron';
import path from 'path';
import url from 'url';
import fs from 'fs';
import isDev from 'electron-is-dev';

// Create a safer console.log that doesn't throw when streams are closed
const safeConsole = {
  log: (...args: any[]) => {
    try {
      console.log(...args);
    } catch (error) {
      // Silently ignore write errors
    }
  },
  error: (...args: any[]) => {
    try {
      console.error(...args);
    } catch (error) {
      // Silently ignore write errors
    }
  }
};

let mainWindow: BrowserWindow | null;

function createWindow() {
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    webPreferences: {
      nodeIntegration: false, // Best practice for security
      contextIsolation: true, // Best practice for security
      preload: path.join(__dirname, 'preload.js'), // Enable preload script for IPC
      devTools: true, // Always enable DevTools for debugging
      webSecurity: true, // Enable web security
    },
    icon: path.join(__dirname, '../public/placeholder-logo.svg') // Adjust path if needed
  });

  // Set Content Security Policy - More secure configuration
  mainWindow.webContents.session.webRequest.onHeadersReceived((details, callback) => {
    callback({
      responseHeaders: {
        ...details.responseHeaders,
        'Content-Security-Policy': [
          "default-src 'self'; " +
          "script-src 'self' 'unsafe-inline' blob: data:; " +
          "style-src 'self' 'unsafe-inline' data:; " +
          "img-src 'self' data: blob: https:; " +
          "font-src 'self' data: blob:; " +
          "connect-src 'self' ws: wss:; " +
          "worker-src 'self' blob: data:; " +
          "child-src 'self' blob: data:; " +
          "frame-src 'self' blob: data:; " +
          "media-src 'self' blob: data:;"
        ]
      }
    });
  });

  // Set a default title
  mainWindow.setTitle('CodeFusion - Modern Code Editor');

  // Determine the correct path to load
  const appPath = app.getAppPath();
  safeConsole.log('App path:', appPath);

  if (isDev || process.argv.includes('--dev')) {
    // In development, load from the Next.js dev server
    safeConsole.log('Loading from dev server');
    mainWindow.loadURL('http://localhost:3333');
    mainWindow.webContents.openDevTools();
  } else {
    // In production, load the statically exported Next.js app
    try {
      // Use a direct path to the index.html file
      const indexFile = path.join(__dirname, '../out/index.html');
      safeConsole.log('Checking for index.html at:', indexFile);

      if (fs.existsSync(indexFile)) {
        safeConsole.log('Found index.html at:', indexFile);
        const indexPath = url.format({
          pathname: indexFile,
          protocol: 'file:',
          slashes: true,
        });

        safeConsole.log('Loading from:', indexPath);
        mainWindow.loadFile(indexFile);
      } else {
        // Try alternative paths
        const possiblePaths = [
          path.join(process.cwd(), 'out/index.html'),
          path.join(app.getAppPath(), 'out/index.html')
        ];

        safeConsole.log('Checking alternative paths:');
        possiblePaths.forEach(p => safeConsole.log(' - ' + p));

        let found = false;
        for (const altPath of possiblePaths) {
          if (fs.existsSync(altPath)) {
            safeConsole.log('Found index.html at:', altPath);
            mainWindow.loadFile(altPath);
            found = true;
            break;
          }
        }

        if (!found) {
          throw new Error('Could not find index.html in any of the expected locations');
        }
      }

      // Only open DevTools in development or when explicitly requested
      if (process.argv.includes('--devtools')) {
        mainWindow.webContents.openDevTools();
      }
    } catch (error) {
      safeConsole.error('Error loading index.html:', error);

      // Show error in window
      mainWindow.loadURL(`data:text/html,<html><body><h1>Error</h1><p>${error}</p></body></html>`);

      // Always open DevTools when there's an error
      mainWindow.webContents.openDevTools();
    }
  }

  // Open external links in the default browser
  mainWindow.webContents.setWindowOpenHandler(({ url }) => {
    shell.openExternal(url);
    return { action: 'deny' };
  });

  mainWindow.on('closed', () => {
    mainWindow = null;
  });
}

app.on('ready', createWindow);

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', () => {
  if (mainWindow === null) {
    createWindow();
  }
});

// IPC handlers for file system operations
ipcMain.handle('select-folder', async () => {
  const result = await dialog.showOpenDialog(mainWindow!, {
    properties: ['openDirectory'],
    title: 'Select Project Folder'
  });

  if (!result.canceled && result.filePaths.length > 0) {
    const folderPath = result.filePaths[0];
    return {
      success: true,
      path: folderPath,
      name: path.basename(folderPath)
    };
  }

  return { success: false };
});

ipcMain.handle('read-directory', async (event, dirPath: string) => {
  try {
    const items = fs.readdirSync(dirPath, { withFileTypes: true });
    const result = [];

    for (const item of items) {
      const itemPath = path.join(dirPath, item.name);
      const stats = fs.statSync(itemPath);

      if (item.isDirectory()) {
        result.push({
          id: Date.now() + Math.random(),
          name: item.name,
          type: 'folder',
          path: itemPath,
          expanded: false,
          files: []
        });
      } else {
        const ext = path.extname(item.name).slice(1);
        result.push({
          id: Date.now() + Math.random(),
          name: item.name,
          type: ext || 'file',
          path: itemPath,
          size: stats.size,
          modified: stats.mtime
        });
      }
    }

    return { success: true, items: result };
  } catch (error) {
    safeConsole.error('Error reading directory:', error);
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
  }
});

ipcMain.handle('read-file', async (event, filePath: string) => {
  try {
    const content = fs.readFileSync(filePath, 'utf-8');
    return { success: true, content };
  } catch (error) {
    safeConsole.error('Error reading file:', error);
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
  }
});

ipcMain.handle('save-file', async (event, filePath: string, content: string) => {
  try {
    fs.writeFileSync(filePath, content, 'utf-8');
    return { success: true };
  } catch (error) {
    safeConsole.error('Error saving file:', error);
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
  }
});

ipcMain.handle('create-file', async (event, filePath: string, content: string = '') => {
  try {
    // Check if file already exists
    if (fs.existsSync(filePath)) {
      return { success: false, error: 'File already exists' };
    }

    // Create directory if it doesn't exist
    const dir = path.dirname(filePath);
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }

    fs.writeFileSync(filePath, content, 'utf-8');
    return { success: true };
  } catch (error) {
    safeConsole.error('Error creating file:', error);
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
  }
});

ipcMain.handle('delete-file', async (event, filePath: string) => {
  try {
    const stats = fs.statSync(filePath);
    if (stats.isDirectory()) {
      fs.rmSync(filePath, { recursive: true, force: true });
    } else {
      fs.unlinkSync(filePath);
    }
    return { success: true };
  } catch (error) {
    safeConsole.error('Error deleting file:', error);
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
  }
});