"use client"

import { KanbanBoard } from "@/components/kanban/kanban-board"
import { useBoard } from "@/components/kanban/board-context"
import { useEffect } from "react";
import { useSearchParams } from "next/navigation";

export default function HomePage() {
  const { setActiveBoard, activeBoard } = useBoard();
  const searchParams = useSearchParams();
  const boardIdFromUrl = searchParams.get('board');

  // Set the active board based on URL or default to 'main'
  useEffect(() => {
    if (boardIdFromUrl) {
      setActiveBoard(boardIdFromUrl);
    } else if (!activeBoard) {
      setActiveBoard("main"); // Default board if no ID in URL
    }
  }, [boardIdFromUrl, setActiveBoard, activeBoard]);

  return (
    <main className="flex-1 container mx-auto p-4 md:p-6 overflow-hidden">
      <KanbanBoard />
    </main>
  );
}