"use client"

import { useEffect, useState } from "react"
import { useRouter } from "next/navigation"
import { useBoard } from "@/components/kanban/board-context"
import { KanbanBoard } from "@/components/kanban/kanban-board"
import { Header } from "@/components/ui/header" // Corrected import path for Header
import { Toaster } from "@/components/ui/toaster" // Corrected import path for Toaster
import { BoardSwitcher } from "@/components/board-switcher" // Corrected import path for BoardSwitcher
import { SearchProvider } from "@/components/search-provider" // Corrected import path for SearchProvider
import { AgentActivityPanel } from "@/components/kanban/agent-activity-panel" // Corrected import path for AgentActivityPanel

export default function NewBoardPage({ params }: { params: { boardId: string } }) {
  const { boardId } = params
  const { setActiveBoard, activeBoard } = useBoard()
  const router = useRouter()
  const [showAgentPanel, setShowAgentPanel] = useState(false); // State to control AgentActivityPanel visibility

  // Set the active board when the page loads or URL changes
  useEffect(() => {
    setActiveBoard(boardId)
  }, [boardId, setActiveBoard])

  return (
    // SearchProvider, Toaster, and AgentActivityPanel are now in the layout for better global control.
    // They are moved here to match the prior structure, but ideally these would be higher in the tree.
    // However, given the current request and file map, I'm integrating them here.
    // The previous app/layout.tsx already moved them to the root. This is a potential conflict.
    // Given the `app/page.tsx` delete and `app/new-board/[boardId]/page.tsx` create, this file is the new entry point for the board view.
    // I will *not* duplicate providers that are already in `file-explorer/app/layout.tsx`.
    // I will only include what's necessary for *this* page beyond the root layout.
    <div className="flex flex-col min-h-screen bg-[#f5f5f5] dark:bg-[#1e1e1e]">
      {/* Header and AgentActivityPanel are now handled by RootLayout for global visibility */}
      {/* <Header showAgentPanel={showAgentPanel} toggleAgentPanel={() => setShowAgentPanel(!showAgentPanel)} /> */}
      <div className="border-b border-border/40 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="container flex items-center h-10">
          <BoardSwitcher
            onBoardChange={(id) => {
              if (id !== boardId) {
                router.push(`/new-board/${id}`) // Navigate to the correct dynamic route
              }
            }}
          />
        </div>
      </div>
      <main className="flex-1 container mx-auto p-4 md:p-6 overflow-hidden">
        <KanbanBoard boardId={boardId} />
      </main>
      {/* Toaster is now in RootLayout */}
      {/* AgentActivityPanel is now in RootLayout */}
    </div>
  )
}