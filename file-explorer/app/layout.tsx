import type React from "react"
import type { Metada<PERSON> } from "next"
import { Inter } from "next/font/google"
import "./globals.css"
import { ThemeProvider } from "@/components/ui/theme-provider" // Assuming common UI components in ui folder
import { BoardProvider } from "@/components/kanban/board-context"
import { AgentBoardControllerProvider } from "@/components/kanban/agent-board-controller"
import { Toaster } from "@/components/ui/toaster" // Assuming common UI components in ui folder
import { SearchProvider } from "@/components/search-provider" // SearchProvider is now a global component
import { Suspense } from "react"
import { Header } from "@/components/ui/header" // Header is now a global UI component
import { useState } from "react" // Needed for Header state

const inter = Inter({ subsets: ["latin"] })

export const metadata: Metadata = {
  title: "Synapse - AI-Powered Development Environment",
  description: "A comprehensive AI-assisted software development environment with Kanban task management and intelligent agents.",
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  // Local state for header to control AgentActivityPanel visibility
  // This state management pattern should be refined if there's a more global state solution
  const [showAgentPanel, setShowAgentPanel] = useState(false);

  return (
    <html lang="en" suppressHydrationWarning>
      <body className={inter.className}>
        <ThemeProvider attribute="class" defaultTheme="system" enableSystem storageKey="synapse-theme">
          <SearchProvider>
            <BoardProvider>
              <AgentBoardControllerProvider>
                <Header showAgentPanel={showAgentPanel} toggleAgentPanel={() => setShowAgentPanel(!showAgentPanel)} />
                <div className="flex flex-1 overflow-hidden h-[calc(100vh-3.5rem)]"> {/* Adjust height based on header */}
                  <Suspense>{children}</Suspense>
                  {showAgentPanel && (
                    <div className="h-full">
                      {/* AgentActivityPanel is now a direct child in the layout for consistent visibility */}
                      <AgentActivityPanel />
                    </div>
                  )}
                </div>
                <Toaster />
              </AgentBoardControllerProvider>
            </BoardProvider>
          </SearchProvider>
        </ThemeProvider>
      </body>
    </html>
  )
}
// Note: AgentActivityPanel import and use in layout.tsx is for demonstration of global panel.
// If it should only appear within the Kanban context, keep it within the KanbanBoard component.
// For now, moved here to match "global" agent panel idea from kanban-board/components/agent-activity-panel.tsx
// which was previously part of the "kanban-integration.tsx" mock up.
import { AgentActivityPanel } from "@/components/kanban/agent-activity-panel"