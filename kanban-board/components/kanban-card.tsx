"use client"

import React from "react"

import { useState } from "react"
import { useDraggable } from "@dnd-kit/core"
import { CSS } from "@dnd-kit/utilities"
import { Card as UICard, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import { Toolt<PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { Activity, CalendarIcon, Clock, Link, MoreHorizontal, GripVertical, Network, Settings } from "lucide-react"
import { formatDistanceToNow } from "date-fns"
import type { Card, CardType } from "./kanban-board"
import { CardDetailView } from "./card-detail-view" // Import the CardDetailView component

interface KanbanCardProps {
  card: Card
  onCardUpdate?: (updatedCard: Card) => void
  onDeleteCard?: (cardId: string) => void
  cardTypes: CardType[]
  index?: number
  columnId?: string
  isDragOverlay?: boolean
}

export function KanbanCard({
  card,
  onCardUpdate,
  onDeleteCard,
  cardTypes,
  index,
  columnId,
  isDragOverlay = false,
}: KanbanCardProps) {
  const [isDetailViewOpen, setIsDetailViewOpen] = useState(false)

  const hasDependencies = card.dependencies && card.dependencies.length > 0

  // Skip draggable setup if this is a drag overlay
  const { attributes, listeners, setNodeRef, transform, isDragging } = useDraggable({
    id: card.id,
    data: {
      type: "card",
      card: card, // The full card object
      columnId: columnId, // The ID of the column the card currently belongs to
      index: index, // Original index, useful for reordering within the same column
    },
    disabled: isDragOverlay,
  })

  const style = !isDragOverlay
    ? {
        transform: transform ? CSS.Transform.toString(transform) : undefined,
        opacity: isDragging ? 0.8 : 1,
        zIndex: isDragging ? 1000 : undefined,
        position: isDragging ? ("relative" as const) : undefined,
      }
    : {}

  // Replace the cardTypeDetails finding logic with this more robust version
  const cardTypeDetails = React.useMemo(() => {
    console.log("Finding card type for priority:", card.priority)
    console.log("Available card types:", cardTypes)

    // First try to find by id
    const byId = cardTypes.find((ct) => ct.id === card.priority)
    if (byId) {
      console.log("Found by ID:", byId)
      return byId
    }

    // Then try by name (case insensitive)
    const byName = cardTypes.find((ct) => ct.name.toLowerCase() === (card.priority || "").toLowerCase())
    if (byName) {
      console.log("Found by name:", byName)
      return byName
    }

    // Fallback to default
    console.log("Using default card type:", cardTypes[0])
    return cardTypes[0]
  }, [card.priority, cardTypes])

  // Make sure the color is applied correctly
  const cardTypeColorStyle = cardTypeDetails ? { borderLeft: `4px solid ${cardTypeDetails.color}` } : {}

  // Handle opening the detail view
  const handleOpenDetailView = (e: React.MouseEvent) => {
    if (isDragOverlay) return // Don't open detail view when dragging
    e.stopPropagation()
    setIsDetailViewOpen(true)
  }

  // Handle card update from detail view
  const handleCardUpdate = (updatedCard: Card) => {
    if (onCardUpdate) {
      onCardUpdate(updatedCard)
    }
    setIsDetailViewOpen(false)
  }

  const formatDate = (dateString?: string) => {
    if (!dateString) return null
    try {
      const date = new Date(dateString)
      return formatDistanceToNow(date, { addSuffix: true })
    } catch (error) {
      console.error("Error formatting date:", error)
      return dateString
    }
  }

  return (
    <>
      <div
        ref={!isDragOverlay ? setNodeRef : undefined}
        style={{ ...style, ...cardTypeColorStyle }}
        className={`mb-2 transition-shadow duration-200 rounded-md max-w-full ${
          isDragging || isDragOverlay ? "shadow-xl" : "hover:shadow-lg hover:-translate-y-0.5 cursor-pointer"
        }`}
        onClick={handleOpenDetailView}
      >
        <UICard
          className={`shadow-sm transition-all duration-200 w-full overflow-hidden ${
            isDragging || isDragOverlay ? "opacity-90 border-primary/50" : ""
          }`}
        >
          <CardContent className="p-3 relative overflow-hidden">
            {/* Drag Handle - Only show if not a drag overlay */}
            {!isDragOverlay && (
              <div
                {...attributes}
                {...listeners}
                className="absolute top-1 right-1 p-1.5 rounded-full text-muted-foreground hover:bg-muted hover:text-foreground focus:outline-none focus:ring-1 focus:ring-ring cursor-grab active:cursor-grabbing"
                onClick={(e) => e.stopPropagation()} // Prevent card click when using drag handle
                aria-label="Drag card"
              >
                <GripVertical className="h-4 w-4" />
              </div>
            )}

            {/* Card Header */}
            <div className="flex justify-between items-start mb-1 pr-7">
              <h3 className="font-medium text-sm line-clamp-2 mr-2 overflow-hidden text-ellipsis">{card.title}</h3>
              <button
                onClick={handleOpenDetailView}
                className="text-muted-foreground hover:text-foreground p-0.5 rounded hover:bg-muted"
                aria-label="More options"
              >
                <MoreHorizontal className="h-4 w-4" />
              </button>
            </div>

            {/* Project ID */}
            {card.projectId && <div className="text-xs text-muted-foreground mb-2">{card.projectId}</div>}

            {/* Card Description - Added to show content */}
            {card.description && (
              <div className="text-xs text-muted-foreground mb-2 line-clamp-2 overflow-hidden text-ellipsis">
                {card.description}
              </div>
            )}

            {/* Dependencies indicator */}
            {hasDependencies && (
              <div className="mb-2 flex items-center gap-1 bg-muted/50 p-1 rounded-sm text-xs">
                <Link className="h-3 w-3 text-muted-foreground" />
                <span className="text-muted-foreground">
                  {card.dependencies?.length} {card.dependencies?.length === 1 ? "dependency" : "dependencies"}
                </span>
              </div>
            )}

            {/* Resource usage mini chart */}
            {card.resourceMetrics && (
              <div className="mb-2 bg-muted/40 p-1.5 rounded-sm text-xs">
                <div className="flex justify-between items-center gap-2">
                  <span className="flex items-center">
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Activity className="h-3 w-3 mr-0.5 text-blue-500" />
                        </TooltipTrigger>
                        <TooltipContent>CPU Time</TooltipContent>
                      </Tooltip>
                    </TooltipProvider>{" "}
                    {card.resourceMetrics.cpuTime || 0}%
                  </span>
                  <span className="flex items-center">
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Network className="h-3 w-3 mr-0.5 text-green-500" />
                        </TooltipTrigger>
                        <TooltipContent>Memory Usage</TooltipContent>
                      </Tooltip>
                    </TooltipProvider>{" "}
                    {card.resourceMetrics.memoryUsage || 0}MB
                  </span>
                  <span className="flex items-center">
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Settings className="h-3 w-3 mr-0.5 text-purple-500" />
                        </TooltipTrigger>
                        <TooltipContent>Token Usage</TooltipContent>
                      </Tooltip>
                    </TooltipProvider>{" "}
                    {card.resourceMetrics.tokenUsage || 0}k
                  </span>
                </div>
              </div>
            )}

            {/* Tags and Priority */}
            <div className="flex flex-wrap gap-1 mb-2">
              {/* Priority Badge */}
              {cardTypeDetails && (
                <Badge
                  variant="outline"
                  className="text-[10px] px-1.5 py-0.5 h-5 font-normal"
                  style={{
                    backgroundColor: `${cardTypeDetails.color}20`,
                    color: cardTypeDetails.color,
                    borderColor: `${cardTypeDetails.color}80`,
                  }}
                >
                  {cardTypeDetails.name}
                </Badge>
              )}

              {/* Tags - Show all tags instead of just 2 */}
              {card.tags &&
                card.tags.map((tag) => (
                  <Badge key={tag} variant="secondary" className="text-[10px] px-1.5 py-0.5 h-5 font-normal">
                    {tag}
                  </Badge>
                ))}
            </div>

            {/* Progress Bar */}
            {typeof card.progress === "number" && (
              <div className="space-y-1 mb-2">
                <div className="flex justify-between text-xs text-muted-foreground">
                  <span>Progress</span>
                  <span>{card.progress}%</span>
                </div>
                <Progress value={card.progress} className="h-1" />
              </div>
            )}

            {/* Footer with Agents, Story Points, Due Date */}
            <div className="flex justify-between items-center text-xs text-muted-foreground mt-2">
              {/* Agent Assignments */}
              <div className="flex -space-x-2">
                {card.agentAssignments &&
                  card.agentAssignments.slice(0, 2).map((assignment) => (
                    <TooltipProvider key={assignment.agentId}>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Avatar className="h-5 w-5 border-2 border-background">
                            <AvatarFallback className="text-[9px]">
                              {assignment.agentType.substring(0, 1).toUpperCase()}
                            </AvatarFallback>
                          </Avatar>
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>{assignment.agentType}</p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  ))}
              </div>

              {/* Story Points */}
              {card.storyPoints && (
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Badge variant="outline" className="text-[10px] px-1.5 py-0.5 h-5 font-medium">
                        {card.storyPoints} SP
                      </Badge>
                    </TooltipTrigger>
                    <TooltipContent>Story Points</TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              )}

              {/* Due Date */}
              {card.dueDate && (
                <div className="flex items-center">
                  <CalendarIcon className="h-3 w-3 mr-1" />
                  {formatDate(card.dueDate)}
                </div>
              )}
            </div>

            {/* Subtasks Progress */}
            {card.subtasks && card.subtasks.length > 0 && (
              <div className="mt-2">
                <div className="flex justify-between text-xs text-muted-foreground mb-0.5">
                  <span>Subtasks</span>
                  <span>
                    {card.subtasks.filter((st) => st.completed).length} / {card.subtasks.length}
                  </span>
                </div>
                <Progress
                  value={(card.subtasks.filter((st) => st.completed).length / card.subtasks.length) * 100}
                  className="h-1"
                />
              </div>
            )}

            {/* Last Updated */}
            {card.updatedAt && (
              <div className="flex justify-end mt-2">
                <div className="flex items-center text-xs text-muted-foreground">
                  <Clock className="h-3 w-3 mr-1" />
                  {formatDate(card.updatedAt)}
                </div>
              </div>
            )}
          </CardContent>
        </UICard>
      </div>

      {/* Card Detail View for editing - Only if not a drag overlay */}
      {!isDragOverlay && isDetailViewOpen && (
        <CardDetailView
          card={card}
          onClose={() => setIsDetailViewOpen(false)}
          onUpdate={handleCardUpdate}
          onDelete={() => {
            if (typeof onDeleteCard === "function") {
              onDeleteCard(card.id)
              setIsDetailViewOpen(false)
            }
          }}
        />
      )}
    </>
  )
}
