"use client"
import { useState, useEffect } from "react"
import { ChevronDown, ChevronRight, PlusCircle, Pencil } from "lucide-react"
import { Button } from "@/components/ui/button"
import { useDroppable } from "@dnd-kit/core"
import type { Card, Column, CardType, Agent } from "./kanban-board"
import { KanbanCard } from "./kanban-card"
import { EditColumnDialog } from "./edit-column-dialog"

interface KanbanSwimlaneProps {
  swimlane: {
    id: string
    title: string
    isExpanded: boolean
  }
  columns: Column[]
  getCardsForColumn: (columnId: string) => Card[]
  onAddCard: (columnId: string) => void
  onCardUpdate: (card: Card | { type: string; column: Column }) => void
  onDeleteCard?: (cardId: string) => void
  onToggleExpansion: () => void
  onEditSwimlane: () => void // Add this line
  boardActiveColumnId: string | null // From board state
  boardActiveSwimlaneId: string | null // From board state
  isBoardDragging: boolean // From board state
  cardTypes: CardType[]
  agents?: Agent[]
}

export function KanbanSwimlane({
  swimlane,
  columns,
  getCardsForColumn,
  onAddCard,
  onCardUpdate,
  onDeleteCard,
  onToggleExpansion,
  onEditSwimlane,
  boardActiveColumnId,
  boardActiveSwimlaneId,
  isBoardDragging,
  cardTypes,
  agents, // Pass agents if SwimlaneColumn or KanbanCard uses it
}: KanbanSwimlaneProps) {
  return (
    <div className="border rounded-md overflow-hidden">
      <div className="flex items-center justify-between p-2 bg-muted/50 cursor-pointer">
        <div className="flex items-center gap-2" onClick={onToggleExpansion}>
          {swimlane.isExpanded ? (
            <ChevronDown className="h-4 w-4 text-muted-foreground" />
          ) : (
            <ChevronRight className="h-4 w-4 text-muted-foreground" />
          )}
          <h3 className="font-medium">{swimlane.title}</h3>
        </div>
        <Button
          variant="ghost"
          size="sm"
          className="h-8 w-8 p-0"
          onClick={(e) => {
            e.stopPropagation()
            onEditSwimlane()
          }}
        >
          <Pencil className="h-4 w-4" />
          <span className="sr-only">Edit swimlane</span>
        </Button>
      </div>

      {swimlane.isExpanded && (
        <div className="overflow-x-auto w-full">
          {/* This div itself is not a droppable, pointerEvents: 'none' allows children to be targets */}
          <div className="flex min-w-max relative" style={{ pointerEvents: isBoardDragging ? "none" : "auto" }}>
            {columns.map((column) => (
              <SwimlaneColumn
                key={`${swimlane.id}-${column.id}`} // More unique key
                column={column}
                cards={getCardsForColumn(column.id)}
                onAddCard={() => {
                  onAddCard(column.id)
                }}
                onCardUpdate={onCardUpdate}
                // Determine if this specific cell is the active drop target
                isCellOver={boardActiveColumnId === column.id && boardActiveSwimlaneId === swimlane.id}
                isBoardDragging={isBoardDragging}
                cardTypes={cardTypes}
                swimlaneId={swimlane.id}
                onDeleteCard={(cardId) => {
                  if (typeof onDeleteCard === "function") {
                    onDeleteCard(cardId)
                  }
                }}
                // agents={agents} // Pass agents if needed
              />
            ))}
          </div>
        </div>
      )}
    </div>
  )
}

interface SwimlaneColumnProps {
  column: Column
  cards: Card[]
  onAddCard: () => void
  onCardUpdate: (updatedCard: Card | { type: string; column: Column }) => void
  onDeleteCard?: (cardId: string) => void
  isCellOver: boolean // Is this specific cell the board's active drop target?
  isBoardDragging: boolean
  cardTypes: CardType[]
  swimlaneId: string
  // agents?: Agent[]
}

function SwimlaneColumn({
  column,
  cards,
  onAddCard,
  onCardUpdate,
  onDeleteCard,
  isCellOver, // Use this for styling based on board's active target
  isBoardDragging,
  cardTypes,
  swimlaneId,
  // agents,
}: SwimlaneColumnProps) {
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [columnColor, setColumnColor] = useState<string | null>(null)

  // Initialize or update the column color from metadata when the column changes
  useEffect(() => {
    if (column.metadata?.color) {
      setColumnColor(column.metadata.color)
    }
  }, [column])

  // `isOver` from this hook determines if something is directly over THIS droppable
  const { setNodeRef, isOver: isDirectlyOver } = useDroppable({
    id: `swimlane-column-${column.id}-${swimlaneId}`, // Unique ID for this droppable cell
    data: {
      type: "swimlane-column", // Specific type for swimlane cells
      columnId: column.id,
      swimlaneId: swimlaneId,
    },
  })

  const getColumnWidth = () => {
    return column.subColumns ? "w-[320px]" : "w-[220px]"
  }

  // Combine board-level active target with direct hover for styling
  const showAsDropTarget = isDirectlyOver && isBoardDragging

  const handleSaveColumn = (updatedColumn: Column) => {
    console.log("Saving column with title:", updatedColumn.title, "and color:", updatedColumn.metadata?.color)

    // Update local state immediately for visual feedback
    if (updatedColumn.metadata?.color) {
      setColumnColor(updatedColumn.metadata.color)
    }

    // Create a complete updated column object to send to the parent
    // Make sure we're creating a completely new object with all properties
    const completeUpdatedColumn = {
      ...column,
      title: updatedColumn.title, // Explicitly include the updated title
      metadata: {
        ...column.metadata,
        color: updatedColumn.metadata?.color || columnColor,
      },
    }

    console.log("Complete updated column:", completeUpdatedColumn)
    console.log("Original column title:", column.title, "New title:", completeUpdatedColumn.title)

    // Send the complete updated column to the parent component
    onCardUpdate({
      type: "UPDATE_COLUMN",
      column: completeUpdatedColumn,
    })
  }

  // Get header style based on local state
  const headerStyle = columnColor
    ? {
        backgroundColor: `${columnColor}30`, // 30 = ~20% opacity
        borderBottomColor: columnColor,
        borderBottomWidth: "2px",
      }
    : {}

  return (
    <div
      className={`flex flex-col ${getColumnWidth()} min-h-[100px] max-w-[320px] border-r last:border-r-0`}
      style={{ pointerEvents: "auto" }} // This column itself is interactive
    >
      <div className="p-2 border-b text-center flex items-center justify-between group" style={headerStyle}>
        <h4 className="text-sm font-medium">{column.title}</h4>
        <Button
          variant="ghost"
          size="sm"
          className="h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
          onClick={(e) => {
            e.stopPropagation()
            e.preventDefault()
            setIsEditDialogOpen(true)
          }}
        >
          <Pencil className="h-3 w-3" />
          <span className="sr-only">Edit column</span>
        </Button>
      </div>

      {isEditDialogOpen && (
        <EditColumnDialog
          isOpen={isEditDialogOpen}
          onClose={() => setIsEditDialogOpen(false)}
          column={{
            ...column,
            metadata: {
              ...column.metadata,
              color: columnColor || "#e2e8f0", // Provide a default color if none exists
            },
          }}
          onSave={handleSaveColumn}
        />
      )}

      {column.subColumns ? (
        <div className="flex flex-1">
          {" "}
          {/* Added flex-1 */}
          {column.subColumns.map((subColumn) => (
            <SubColumn
              key={`${swimlaneId}-${column.id}-${subColumn.id}`}
              columnId={column.id}
              subColumn={subColumn}
              // Filter cards for subColumn if you have a way to associate them
              cards={cards /* .filter(card => card.subColumnId === subColumn.id) */}
              onAddCard={onAddCard} // This should ideally be specific to the subColumn
              onCardUpdate={onCardUpdate}
              isCellOver={isCellOver /* && boardActiveSubColumnId === subColumn.id */}
              isBoardDragging={isBoardDragging}
              cardTypes={cardTypes}
              swimlaneId={swimlaneId}
              // agents={agents}
            />
          ))}
        </div>
      ) : (
        // This is the droppable area for cards in this swimlane column cell
        <div
          ref={setNodeRef}
          className={`flex flex-col gap-2 p-2 min-h-[100px] relative flex-1 overflow-hidden
          ${
            // Use `showAsDropTarget` (derived from local `isOver` and board `isDragging`)
            showAsDropTarget
              ? "bg-primary/10 ring-2 ring-inset ring-primary/50 scale-[1.01] shadow-inner"
              : isBoardDragging // If board is dragging but not over this specific cell
                ? "opacity-70" // Generic dragging effect
                : ""
          }
          transition-all duration-150 ease-in-out 
        `}
        >
          {cards.map((card, index) => (
            <div key={card.id} className="animate-in fade-in slide-in-from-top-4 duration-200">
              <KanbanCard
                card={card}
                onCardUpdate={onCardUpdate}
                onDeleteCard={onDeleteCard}
                cardTypes={cardTypes}
                index={index}
                columnId={column.id} // Card's original/current column
                onDeleteCard={(cardId) => {
                  // Pass the delete event up to the parent component
                  if (typeof onDeleteCard === "function") {
                    onDeleteCard(cardId)
                  }
                }}
                // agents={agents}
              />
            </div>
          ))}
          {cards.length === 0 && (
            <div
              className={`
              flex items-center justify-center h-20 rounded-md
              border-2 border-dashed 
              transition-all duration-200
              ${
                showAsDropTarget
                  ? "border-primary/70 bg-primary/10 scale-105"
                  : isBoardDragging
                    ? "border-muted-foreground/10 bg-muted/5"
                    : "border-muted-foreground/20"
              }
            `}
            >
              <p className="text-xs text-muted-foreground">{showAsDropTarget ? "Drop here" : "No cards"}</p>
            </div>
          )}
          <Button
            variant="ghost"
            size="sm"
            className="mt-auto w-full justify-start"
            onClick={(e) => {
              e.preventDefault()
              e.stopPropagation()
              if (typeof onAddCard === "function") {
                onAddCard()
              }
            }}
          >
            <PlusCircle className="h-4 w-4 mr-1" />
            Add Card
          </Button>
        </div>
      )}
    </div>
  )
}

interface SubColumnProps {
  columnId: string // Parent column ID
  subColumn: { id: string; title: string }
  cards: Card[]
  onAddCard: () => void // Should be specific to this sub-column
  onCardUpdate: (updatedCard: Card) => void
  isCellOver: boolean
  isBoardDragging: boolean
  cardTypes: CardType[]
  swimlaneId: string
  // agents?: Agent[]
}

function SubColumn({
  columnId,
  subColumn,
  cards,
  onAddCard,
  onCardUpdate,
  isCellOver,
  isBoardDragging,
  cardTypes,
  swimlaneId,
  // agents,
}: SubColumnProps) {
  const { setNodeRef, isOver: isDirectlyOver } = useDroppable({
    id: `subcolumn-${subColumn.id}-${columnId}-${swimlaneId}`, // Ensure very unique ID
    data: {
      type: "subcolumn",
      columnId: columnId, // Parent column
      subColumnId: subColumn.id,
      swimlaneId: swimlaneId,
    },
  })

  const showAsDropTarget = isDirectlyOver && isBoardDragging

  return (
    <div className="flex-1 flex flex-col border-r last:border-r-0">
      <div className="p-1 border-b bg-muted/10 text-center">
        <h5 className="text-xs font-medium">{subColumn.title}</h5>
      </div>
      <div
        ref={setNodeRef}
        className={`flex flex-col gap-2 p-2 flex-1 min-h-[80px] // Added min-h
          ${
            showAsDropTarget
              ? "bg-primary/10 ring-2 ring-inset ring-primary/50 scale-[1.01] shadow-inner"
              : isBoardDragging
                ? "opacity-70"
                : ""
          }
          transition-all duration-150 ease-in-out
        `}
      >
        {/* Filter cards specifically for this subColumn if you have a card.subColumnId field */}
        {cards
          .filter((c) => true /* c.subColumnId === subColumn.id */)
          .map((card, index) => (
            <div key={card.id} className="animate-in fade-in slide-in-from-top-4 duration-200">
              <KanbanCard
                card={card}
                onCardUpdate={onCardUpdate}
                cardTypes={cardTypes}
                index={index}
                columnId={columnId} // The parent column ID
                // agents={agents}
              />
            </div>
          ))}
        {cards.filter((c) => true /* c.subColumnId === subColumn.id */).length === 0 && (
          <div
            className={`
              flex items-center justify-center h-20 rounded-md
              border-2 border-dashed
              transition-all duration-200
              ${
                showAsDropTarget
                  ? "border-primary/70 bg-primary/10 scale-105"
                  : isBoardDragging
                    ? "border-muted-foreground/10 bg-muted/5"
                    : "border-muted-foreground/20"
              }
            `}
          >
            <p className="text-xs text-muted-foreground">{showAsDropTarget ? "Drop here" : "No cards"}</p>
          </div>
        )}
        <Button variant="ghost" size="sm" className="mt-auto" onClick={onAddCard}>
          <PlusCircle className="h-4 w-4 mr-1" />
          Add Card
        </Button>
      </div>
    </div>
  )
}
